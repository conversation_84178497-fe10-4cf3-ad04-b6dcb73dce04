{"version": "1.0.0", "lastUpdated": "2025-01-21", "description": "站点默认UI配置，包含表格和卡片的所有样式设置", "taskListSettings": {"displayMode": "table", "density": "compact", "enableZebraStriping": false, "selectedPlantId": null, "columnOrder": ["messages", "dispatchReminder", "taskNumber", "projectName", "constructionSite", "strength", "pouring<PERSON><PERSON>od", "completedProgress", "requiredVolume", "completedVolume", "pumpTruck", "constructionUnit", "contactPhone", "supplyTime", "supplyDate", "publishDate", "dispatchedVehicles"], "columnVisibility": {"messages": true, "dispatchReminder": true, "taskNumber": true, "projectName": true, "constructionSite": true, "strength": true, "pouringMethod": true, "completedProgress": true, "requiredVolume": false, "completedVolume": false, "pumpTruck": true, "constructionUnit": true, "contactPhone": false, "supplyTime": true, "supplyDate": true, "publishDate": false, "dispatchedVehicles": true}, "columnWidths": {"messages": 50, "dispatchReminder": 100, "taskNumber": 120, "projectName": 150, "constructionSite": 120, "strength": 80, "pouringMethod": 100, "completedProgress": 120, "requiredVolume": 100, "completedVolume": 100, "pumpTruck": 80, "constructionUnit": 120, "contactPhone": 120, "supplyTime": 100, "supplyDate": 100, "publishDate": 100, "dispatchedVehicles": 300}, "columnTextStyles": {"taskNumber": {"color": "primary", "fontWeight": "font-medium"}, "projectName": {"color": "dark-gray", "fontWeight": "font-semibold"}, "strength": {"color": "amber", "fontWeight": "font-medium"}, "dispatchReminder": {"color": "destructive", "fontWeight": "font-bold"}, "completedProgress": {"color": "lime", "fontWeight": "font-medium"}}, "columnBackgrounds": {"messages": "primary-light-15", "dispatchReminder": "destructive-light-15", "projectName": "primary-light-15", "strength": "accent-light-15", "dispatchedVehicles": "muted-light-15", "productionLines": "muted-light-15"}, "inTaskVehicleCardStyles": {"cardWidth": "w-14", "cardHeight": "h-8", "fontSize": "text-[12px]", "fontColor": "text-foreground", "vehicleNumberFontWeight": "font-medium", "cardBgColor": "bg-card/80", "cardGradient": "from-blue-50 to-blue-100", "gradientEnabled": false, "gradientDirection": "to-r", "gradientStartColor": "#3b82f6", "gradientEndColor": "#8b5cf6", "statusDotSize": "w-1 h-1", "borderRadius": "rounded-md", "boxShadow": "shadow-sm", "vehiclesPerRow": 4, "hoverEffect": "hover:shadow-md hover:scale-105", "borderColor": "border-slate-200", "borderWidth": "border", "statusColors": {"pending": {"bgColor": "bg-yellow-100", "textColor": "text-yellow-800", "borderColor": "border-yellow-200", "dotColor": "bg-yellow-500"}, "outbound": {"bgColor": "bg-blue-100", "textColor": "text-blue-800", "borderColor": "border-blue-200", "dotColor": "bg-blue-500"}, "returned": {"bgColor": "bg-green-100", "textColor": "text-green-800", "borderColor": "border-green-200", "dotColor": "bg-green-500"}, "maintenance": {"bgColor": "bg-red-100", "textColor": "text-red-800", "borderColor": "border-red-200", "dotColor": "bg-red-500"}, "inactive": {"bgColor": "bg-gray-100", "textColor": "text-gray-600", "borderColor": "border-gray-200", "dotColor": "bg-gray-400"}}, "compactMode": {"enabled": false, "cardWidth": "w-12", "cardHeight": "h-6", "fontSize": "text-[8px]", "vehiclesPerRow": 5}, "animationDuration": "transition-all duration-200", "spacing": "gap-1"}, "tableStyleConfig": {"headerStyle": {"backgroundColor": "bg-slate-100", "textColor": "text-slate-700", "fontSize": "text-sm", "fontWeight": "font-semibold", "borderColor": "border-slate-200", "padding": "px-3 py-2"}, "rowStyle": {"evenRowBg": "bg-white", "oddRowBg": "bg-slate-50", "hoverBg": "hover:bg-blue-50", "borderColor": "border-slate-200", "textColor": "text-slate-700", "fontSize": "text-sm", "padding": "px-3 py-2"}, "cellAlignment": {"messages": "text-center", "dispatchReminder": "text-center", "taskNumber": "text-left", "projectName": "text-left", "constructionSite": "text-left", "strength": "text-center", "pouringMethod": "text-left", "completedProgress": "text-center", "requiredVolume": "text-right", "completedVolume": "text-right", "pumpTruck": "text-left", "constructionUnit": "text-left", "contactPhone": "text-left", "supplyTime": "text-center", "supplyDate": "text-center", "publishDate": "text-center", "dispatchedVehicles": "text-left"}, "columnPriority": {"taskNumber": "high", "projectName": "high", "constructionSite": "high", "strength": "medium", "dispatchedVehicles": "high", "completedProgress": "medium", "supplyTime": "medium", "supplyDate": "medium", "messages": "low", "dispatchReminder": "medium"}, "stickyColumnStyle": {"backgroundColor": "bg-white", "borderColor": "border-slate-200", "shadowRight": "shadow-[2px_0_4px_-1px_rgba(0,0,0,0.1)]", "zIndex": "z-10"}}, "groupConfig": {"groupBy": "none", "enabled": false, "collapsible": true, "defaultCollapsed": [], "sortOrder": "asc", "showGroupStats": true, "allowedGroupColumns": ["projectName", "strength", "pouring<PERSON><PERSON>od", "supplyDate", "pumpTruck", "constructionUnit", "constructionSite"], "disallowedGroupColumns": ["taskNumber", "vehicleCount", "completedVolume", "requiredVolume", "contactPhone", "supplyTime", "publishDate", "dispatchedVehicles"], "groupHeaderStyle": {"backgroundColor": "bg-muted/50", "textColor": "text-foreground", "fontSize": "text-sm", "fontWeight": "font-medium", "padding": "py-2"}}}, "taskCardConfig": {"style": {"theme": "default", "borderRadius": "md", "shadow": "sm", "animation": "subtle", "spacing": "normal"}, "areas": {"top": {"visible": true, "fields": {"messageIcon": {"visible": true, "fontSize": "sm", "fontWeight": "normal", "color": "primary", "textAlign": "left"}, "projectName": {"visible": true, "fontSize": "sm", "fontWeight": "semibold", "color": "default", "textAlign": "left"}, "constructionSite": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "dispatchReminder": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "warning", "textAlign": "right"}, "strength": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "warning", "textAlign": "right"}, "progressRing": {"visible": true, "fontSize": "sm", "fontWeight": "medium", "color": "primary", "textAlign": "center"}}}, "vehicle": {"visible": true, "height": "normal", "fields": {"vehicleCount": {"visible": true, "fontSize": "sm", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "vehicleCards": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "default", "textAlign": "left"}}}, "content": {"visible": true, "height": "auto", "layout": "double", "fields": {"requiredVolume": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "completedVolume": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "scheduledTime": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "contactPhone": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "completedProgress": {"visible": true, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "estimatedDuration": {"visible": false, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "constructionLocation": {"visible": false, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}, "taskStatus": {"visible": false, "fontSize": "xs", "fontWeight": "medium", "color": "default", "textAlign": "left"}}}, "bottom": {"visible": true, "layout": "single", "fields": {"customerName": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "createdAt": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "taskNumber": {"visible": true, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}, "updatedAt": {"visible": false, "fontSize": "xs", "fontWeight": "normal", "color": "muted", "textAlign": "left"}}}}}, "cardViewConfig": {"size": "small", "layout": "standard", "theme": "default", "spacing": "normal", "borderRadius": "medium", "shadow": "medium", "animation": "smooth", "columns": "auto", "cardSpacing": "gap-4", "cardPadding": "p-4", "cardBorder": "border border-slate-200", "cardBackground": "bg-white", "cardHover": "hover:shadow-lg hover:border-blue-300", "gridLayout": {"minCardWidth": "280px", "maxCardWidth": "320px", "cardHeight": "auto", "responsiveColumns": {"sm": 1, "md": 2, "lg": 3, "xl": 4, "2xl": 5}}, "colorScheme": {"primary": "blue", "secondary": "slate", "accent": "orange", "success": "green", "warning": "yellow", "danger": "red"}}, "performanceConfig": {"virtualScroll": {"enabled": true, "overscan": 1, "increaseViewportBy": 100, "itemHeight": 300}, "animations": {"enabled": true, "duration": 250, "easing": "ease-out", "reduceMotion": false}, "rendering": {"useMemo": true, "useCallback": true, "reactMemo": true, "debounceMs": 100}, "scrolling": {"smoothScrolling": true, "scrollBehavior": "smooth", "throttleMs": 16}}}