
// Ensure to keep other existing types and export them as well.

export interface Plant {
  id: string;
  name: string;
  stats: {
    completedTasks: number;
    totalTasks: number;
  };
  productionLineCount: number;
}
export interface Task {
  vehicles: any;
  deliveryStatus: string;
  dispatchReminderMinutes: number;
  id: string;
  plantId: string;
  taskNumber: string;
  projectName: string;
  projectAbbreviation: string;
  constructionUnit: string;
  constructionSite: string;
  strength: string;
  pouringMethod: string;
  vehicleCount: number;
  completedVolume: number;
  requiredVolume: number;
  pumpTruck: string;
  otherRequirements: string;
  contactPhone: string;
  subTheme?: string;
  dispatchNote?: string;
  supplyTime: string;
  supplyDate: string;
  publishDate: string;
  timing?: string; // e.g., "00:00:00"
  dispatchStatus: 'New' | 'ReadyToProduce' | 'RatioSet' | 'InProgress' | 'Paused' | 'Completed' | 'Cancelled';
  isTicketed?: boolean;

  // Fields for Dispatch Reminder, managed by store/worker
  dispatchFrequencyMinutes?: number;
  lastDispatchTime?: string; // ISO string
  nextScheduledDispatchTime?: string; // ISO string, calculated by worker/store
  isDueForDispatch?: boolean; // True if within reminder window, calculated by worker/store
  defaultDispatchFrequencyMinutes?: number; // 默认发车频率，用于未配置发车频率的任务
  minutesToDispatch?: number; // 距离下次发车的分钟数，用于多级提醒

  // 新增字段
  constructionCompany?: string;  // 施工单位
  constructionLocation?: string; // 施工位置
  productionLineCount?: number;  // 生产线数量
  customerName?: string;         // 客户名称
  address?: string;              // 详细地址
  notes?: string;                // 备注信息
  createdAt?: string;            // 创建时间
  scheduledTime?: string;        // 计划时间
  estimatedDuration?: number;    // 预计时长（分钟）

  // 消息相关字段
  unreadMessageCount?: number;   // 未读消息数量
  hasNewMessages?: boolean;      // 是否有新消息
  messages?: TaskMessage[];      // 任务消息列表
}

export type DeliveryOrderStatus = 'newlyDispatched' | 'inProduction' | 'weighed' | 'shipped';

// 任务消息类型
export interface TaskMessage {
  id: string;
  taskId: string;
  content: string;
  sender: string;        // 发送者（工地名称或联系人）
  timestamp: string;     // 发送时间
  isRead: boolean;       // 是否已读
  priority: 'low' | 'normal' | 'high' | 'urgent'; // 优先级
  type: 'info' | 'warning' | 'urgent' | 'question'; // 消息类型
}

export interface DeliveryOrder {
  id: string;
  plantId: string;
  productionLineId: string;
  vehicleNumber: string;
  vehicleStatusIcon?: string;
  driver: string;
  volume: number;
  strength: string;
  projectName: string;
  taskNumber: string;
  status?: DeliveryOrderStatus;
  customerName?: string;
  notes?: string;
}

export interface Vehicle {
  isDragging: unknown;
  id: string;
  vehicleNumber: string;
  status: 'pending' | 'returned' | 'outbound';
  type: 'Tanker' | 'Pump' | 'Other';
  operationalStatus?: 'normal' | 'paused' | 'deactivated';
  assignedTaskId?: string;
  assignedProductionLineId?: string;
  currentTripType?: 'outboundLeg' | 'returnLeg';
  productionStatus?: 'queued' | 'producing' | 'produced' | 'weighed' | 'ticketed' | 'shipped';
  allowWeighRoomEdit?: boolean;
  deliveryOrderId?: string;
  plantId?: string;
  lastTripWashedWithPumpWater?: boolean;
}

export interface InTaskVehicleCardStyle {
  gap: number;
  cardSize: string;
  cardWidth: 'w-8' | 'w-9' | 'w-10' | 'w-12' | 'w-14' | 'w-16' | 'w-20' | 'w-24';
  cardHeight: 'h-7' | 'h-8' | 'h-9' | 'h-10';
  fontSize: 'text-[8px]' | 'text-[9px]' | 'text-[10px]' | 'text-[11px]' | 'text-[12px]';
  fontColor: string;
  vehicleNumberFontWeight: 'font-normal' | 'font-medium' | 'font-semibold' | 'font-bold';
  cardBgColor: string;
  cardGradient?: string;
  // 新增渐变配置
  gradientEnabled?: boolean;
  gradientDirection?: 'to-r' | 'to-l' | 'to-t' | 'to-b' | 'to-tr' | 'to-tl' | 'to-br' | 'to-bl' | 'radial';
  gradientStartColor?: string;
  gradientEndColor?: string;
  statusDotSize: 'w-0.5 h-0.5' | 'w-[3px] h-[3px]' | 'w-1 h-1' | 'w-[5px] h-[5px]' | 'w-1.5 h-1.5';
  borderRadius: string;
  boxShadow: string;
  vehiclesPerRow?: 2 | 3 | 4 | 5 | 6 | 7 | 8;
  className?: string;
  style?: React.CSSProperties;
}

export type TaskColumnId =
  | 'dispatchReminder'
  | 'taskNumber'
  | 'projectName'
  | 'projectAbbreviation'
  | 'constructionUnit'
  | 'constructionSite'
  | 'strength'
  | 'pouringMethod'
  | 'vehicleCount'
  | 'completedProgress'
  | 'requiredVolume'
  | 'completedVolume'
  | 'pumpTruck'
  | 'otherRequirements'
  | 'contactPhone'
  | 'supplyTime'
  | 'supplyDate'
  | 'publishDate'
  | 'timing'
  | 'messages'
  | 'dispatchedVehicles'
  | 'productionLines';

/**
 * 任务分组配置接口
 * 定义任务列表的分组显示方式
 */
export interface TaskGroupConfig {
  /** 分组字段 */
  groupBy: TaskColumnId | 'none';
  /** 是否启用分组 */
  enabled: boolean;
  /** 分组是否可折叠 */
  collapsible: boolean;
  /** 默认折叠的分组 */
  defaultCollapsed: string[];
  /** 分组排序方式 */
  sortOrder: 'asc' | 'desc';
  /** 是否显示分组统计 */
  showGroupStats: boolean;
  /** 可分组的列 */
  allowedGroupColumns: TaskColumnId[];
  /** 禁止分组的列 */
  disallowedGroupColumns: TaskColumnId[];
  /** 分组样式配置 */
  groupHeaderStyle: {
    backgroundColor: string;
    textColor: string;
    fontSize: 'text-sm' | 'text-base' | 'text-lg';
    fontWeight: 'font-normal' | 'font-medium' | 'font-semibold' | 'font-bold';
    padding: 'py-1' | 'py-2' | 'py-3';
  };
}

/**
 * 任务分组统计信息
 */
export interface TaskGroupStats {
  /** 总任务数 */
  total: number;
  /** 已分派任务数 */
  dispatched: number;
  /** 配送中任务数 */
  inDelivery: number;
  /** 已完成任务数 */
  completed: number;
  /** 待处理任务数 */
  pending: number;
}

/**
 * 任务分组数据结构
 */
export interface TaskGroup {
  /** 分组键值 */
  key: string;
  /** 分组显示名称 */
  label: string;
  /** 分组内的任务列表 */
  tasks: Task[];
  /** 是否折叠 */
  collapsed: boolean;
 
}


export interface CustomColumnDefinition {
  id: TaskColumnId;
  label: string;
  isMandatory?: boolean;
  defaultVisible?: boolean;
  isReorderable?: boolean;
  isResizable?: boolean;
  defaultWidth?: number;
  minWidth?: number;
  maxWidth?: number; // Added maxWidth
  isStyleable?: boolean;
  fixed?: 'left' | 'right';
  getColumnBackgroundProps?: (headerId:string,isHeader: boolean, isFixed: boolean) => InTaskVehicleCardStyle;
  densityStyles: any;
  order?: number;
}

export type StyleableColumnId = Extract<TaskColumnId,
  | 'dispatchReminder'
  | 'taskNumber'
  | 'projectName'
  | 'projectAbbreviation'
  | 'constructionUnit'
  | 'constructionSite'
  | 'strength'
  | 'pouringMethod'
  | 'vehicleCount'
  | 'completedProgress'
  | 'requiredVolume'
  | 'completedVolume'
  | 'pumpTruck'
  | 'otherRequirements'
  | 'contactPhone'
  | 'supplyTime'
  | 'supplyDate'
  | 'publishDate'
  | 'timing'
  | 'messages'
  | 'dispatchedVehicles'
  | 'productionLines'
>;

export type ColumnTextStyle = {
  color?: string;
  fontSize?: string;
  fontWeight?: string;
};

export type ColumnTextStyles = {
  [K in StyleableColumnId]?: ColumnTextStyle;
};


export type TaskListDensityMode = "loose" | "normal" | "compact" ;
export type TaskListDisplayMode = "table" | "card";

export interface DensityStyleValues {
  headerPaddingX: string;
  headerPaddingY: string;
  headerHeight: string;
  headerFontSize: string;
  cellPaddingX: string;
  cellPaddingY: string;
  cellFontSize: string;
  cellFontWeight: string;
  productionLineBoxSize: string;
  productionLineBoxFontSize: string;
  productionLineBoxNumericWidth: number;
  productionLineBoxGap: number;
  cellHorizontalPaddingNumeric: number;
}

export type VehicleDisplayMode = 'licensePlate' | 'internalId';

export type CrossPlantDispatchInfo = {
  vehicle: Vehicle | null;
  sourcePlant: Plant | null;
  targetPlant: Plant | null;
};

export interface TaskListStoredSettings {
  [x: string]: any;
  displayMode: TaskListDisplayMode;
  density: Exclude<TaskListDensityMode, '' | 'card'>;
  enableZebraStriping: boolean;
  columnOrder: string[];
  columnVisibility: Record<string, boolean>;
  columnWidths: Record<string, number>;
  columnTextStyles: ColumnTextStyles;
  columnBackgrounds: Record<string, string>; // Value is the 'value' from BackgroundColorOption
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  selectedPlantId: string | null; // Kept for potential direct use though AppStore also has it
  /** 任务分组配置 */
  groupConfig: TaskGroupConfig;
  /** 表格样式配置 */
  tableStyleConfig?: {
    headerStyle?: {
      backgroundColor?: string;
      textColor?: string;
      fontSize?: string;
      fontWeight?: string;
      borderColor?: string;
      padding?: string;
    };
    rowStyle?: {
      evenRowBg?: string;
      oddRowBg?: string;
      hoverBg?: string;
      borderColor?: string;
      textColor?: string;
      fontSize?: string;
      padding?: string;
    };
    cellAlignment?: Record<string, string>;
    columnPriority?: Record<string, string>;
    stickyColumnStyle?: {
      backgroundColor?: string;
      borderColor?: string;
      shadowRight?: string;
      zIndex?: string;
    };
  };
}

// Types for Web Worker communication
export interface ReminderWorkerInput {
  tasks: Task[]; // Consider sending a more optimized Task structure if full Task is too large
  currentTime: number;
  reminderWindowMinutes: number;
}

export interface TaskUpdateForWorker { // Sent from worker to main thread
  id: string;
  nextScheduledDispatchTime?: string; // ISO string or undefined
  isDueForDispatch?: boolean;
  minutesToDispatch?: number; // 添加时间差（分钟）用于多级提醒
}

export interface ReminderWorkerOutput {
  tasksWithUpdates: TaskUpdateForWorker[];
  processedTime: number; // Timestamp from worker when processing finished
  error?: string; // 错误信息
  errorDetails?: string; // 详细错误信息，如堆栈跟踪
  recovery?: boolean; // 是否为恢复消息
  lastSuccessfulRun?: { time: number; taskCount: number }; // 上次成功运行信息
  stats?: { // 处理统计信息
    taskCount: number; // 处理的任务总数
    updatedCount: number; // 更新的任务数量
    processingTimeMs: number; // 处理时间（毫秒）
  };
  messages?: ReminderMessage[]; // 新的提醒消息
  messageType?: 'NEW_MESSAGES' | 'ALL_MESSAGES'; // 消息类型
}

// 新增的提醒消息类型定义
export type ReminderType = 
  | 'dispatchCountdown' 
  | 'highlight' 
  | 'popup' 
  | 'sound'
  | 'error'; // 添加错误类型

export interface ReminderMessage {
  id: string;
  type: ReminderType;
  taskId: string;
  taskNumber: string;
  title: string;
  description: string;
  time: number; // 触发时间戳
  read: boolean;
  projectName?: string;
  extra?: any; // 扩展字段，用于存储不同类型提醒可能需要的额外数据
}

export interface ReminderConfig {
  enabled: boolean;
  taskId: string;
  reminderTypes: ReminderType[];
  reminderFrequencyMinutes: number;
  
  // 增加多级提醒设置
  reminderLevels: {
    minutes: number; // 提前多少分钟提醒
    types: ReminderType[]; // 该级别使用哪些提醒方式
  }[];
  
  // 设置任务默认发车频率（如果任务本身没有设置）
  defaultDispatchFrequencyMinutes?: number;
  
  // 自定义设置
  customSettings?: Record<string, any>;
  
  // 持久化标识，由系统自动管理
  lastUpdated?: number; // 最后更新时间戳
}

// InTaskVehicleCard specific props
export interface InTaskVehicleCardProps {
  vehicle: Vehicle;
  task?: Task; 
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount?: number; 
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string, deliveryOrderId?: string) => void;
  onOpenContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task?: Task) => void;
  density?: Exclude<TaskListDensityMode, '' | 'card'>;
  attributes?: Partial<React.HTMLAttributes<HTMLDivElement>>;
  listeners?: Partial<React.HTMLAttributes<HTMLDivElement>>;
  setNodeRef?: (node: HTMLElement | null) => void;
  style?: React.CSSProperties;
  isDragging?: boolean;
  isOverlay?: boolean;
  isDispatchPanelView?: boolean; 
  isTableCellView?: boolean; // New prop
}
