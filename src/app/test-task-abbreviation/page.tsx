'use client';

import React, { useState } from 'react';
import { TaskAbbreviationModal } from '@/components/modals/task-abbreviation-modal';
import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { Task } from '@/types';

// 模拟任务数据
const mockTask: Task = {
  id: 'test-task-1',
  plantId: 'plant-1',
  taskNumber: 'T001',
  projectName: '测试工程项目建设',
  projectAbbreviation: '测试项目',
  constructionUnit: '测试建设单位有限公司',
  constructionSite: '地下室基础',
  strength: 'C30',
  pouringMethod: '泵送',
  vehicleCount: 5,
  completedVolume: 50,
  requiredVolume: 100,
  pumpTruck: '62米',
  otherRequirements: '无特殊要求',
  contactPhone: '13800138000',
  supplyTime: '08:00',
  supplyDate: '2024-01-15',
  publishDate: '2024-01-14',
  dispatchStatus: 'ReadyToProduce',
  vehicles: [],
  deliveryStatus: 'pending',
  dispatchReminderMinutes: 30,
  timing: '08:00:00',
  isTicketed: false,
  constructionCompany: '中建三局集团有限公司',
  constructionLocation: '地下室基础浇筑',
  productionLineCount: 2,
  customerName: '测试客户',
  address: '测试地址123号',
  notes: '这是一个测试任务',
  createdAt: '2024-01-14T10:00:00Z',
  scheduledTime: '2024-01-15T08:00:00Z',
  estimatedDuration: 120,
  unreadMessageCount: 0,
  hasNewMessages: false,
  messages: []
};

export default function TestTaskAbbreviationPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState<Task>(mockTask);
  const { toast } = useToast();

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSaveAbbreviation = async (taskId: string, abbreviation: string) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 更新本地状态
    setCurrentTask(prev => ({
      ...prev,
      projectAbbreviation: abbreviation
    }));

    toast({
      title: '保存成功',
      description: `任务缩写已更新为：${abbreviation}`,
    });
  };

  return (
    <div className="container mx-auto p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">任务缩写设置功能测试</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">当前任务信息</h2>
          <div className="grid grid-cols-1 gap-4 text-sm">
            <div className="p-3 bg-blue-50 rounded">
              <span className="font-medium text-blue-800">任务编号:</span> 
              <span className="ml-2">{currentTask.taskNumber}</span>
            </div>
            <div className="p-3 bg-green-50 rounded">
              <span className="font-medium text-green-800">当前缩写:</span> 
              <span className="ml-2 font-mono text-lg">
                {currentTask.projectAbbreviation || '(未设置)'}
              </span>
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <span className="font-medium">工程名称:</span> 
              <span className="ml-2">{currentTask.projectName}</span>
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <span className="font-medium">施工部位:</span> 
              <span className="ml-2">{currentTask.constructionSite}</span>
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <span className="font-medium">施工单位:</span> 
              <span className="ml-2">{currentTask.constructionCompany}</span>
            </div>
          </div>
        </div>

        <div className="text-center">
          <Button 
            onClick={handleOpenModal}
            size="lg"
            className="px-8 py-3"
          >
            <FileText className="mr-2 h-5 w-5" />
            设置任务缩写
          </Button>
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">功能说明</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 点击按钮将打开任务缩写设置模态框</li>
            <li>• 第一行是可编辑的任务缩写输入框</li>
            <li>• 第二、三、四行分别显示工程名称、施工部位、施工单位（只读）</li>
            <li>• 底部右侧有取消和确定按钮</li>
            <li>• 点击确定会调用API更新任务缩写</li>
          </ul>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">测试步骤</h3>
          <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
            <li>点击"设置任务缩写"按钮</li>
            <li>在弹出的模态框中修改任务缩写</li>
            <li>查看工程名称、施工部位、施工单位是否正确显示且不可编辑</li>
            <li>点击"确定"按钮保存</li>
            <li>观察页面上的"当前缩写"是否更新</li>
          </ol>
        </div>
      </div>

      {/* 任务缩写模态框 */}
      <TaskAbbreviationModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        task={currentTask}
        onSave={handleSaveAbbreviation}
      />
    </div>
  );
}
