'use client';

import React, { useState } from 'react';
import { QRCodeModal } from '@/components/modals/qr-code-modal';
import { Button } from '@/components/ui/button';
import { QrCode } from 'lucide-react';
import type { Task } from '@/types';

// 模拟任务数据
const mockTask: Task = {
  id: 'test-task-1',
  plantId: 'plant-1',
  taskNumber: 'T001',
  projectName: '测试工程项目',
  projectAbbreviation: '测试项目',
  constructionUnit: '测试建设单位',
  constructionSite: '测试工地',
  strength: 'C30',
  pouringMethod: '泵送',
  vehicleCount: 5,
  completedVolume: 50,
  requiredVolume: 100,
  pumpTruck: '62米',
  otherRequirements: '无特殊要求',
  contactPhone: '13800138000',
  supplyTime: '08:00',
  supplyDate: '2024-01-15',
  publishDate: '2024-01-14',
  dispatchStatus: 'ReadyToProduce',
  vehicles: [],
  deliveryStatus: 'pending',
  dispatchReminderMinutes: 30,
  timing: '08:00:00',
  isTicketed: false,
  constructionCompany: '测试施工公司',
  constructionLocation: '测试施工位置',
  productionLineCount: 2,
  customerName: '测试客户',
  address: '测试地址123号',
  notes: '这是一个测试任务',
  createdAt: '2024-01-14T10:00:00Z',
  scheduledTime: '2024-01-15T08:00:00Z',
  estimatedDuration: 120,
  unreadMessageCount: 0,
  hasNewMessages: false,
  messages: []
};

export default function TestQRCodePage() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="container mx-auto p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">二维码功能测试</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">测试任务信息</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">任务编号:</span> {mockTask.taskNumber}
            </div>
            <div>
              <span className="font-medium">工程名称:</span> {mockTask.projectName}
            </div>
            <div>
              <span className="font-medium">施工工地:</span> {mockTask.constructionSite}
            </div>
            <div>
              <span className="font-medium">混凝土强度:</span> {mockTask.strength}
            </div>
            <div>
              <span className="font-medium">需求方量:</span> {mockTask.requiredVolume}m³
            </div>
            <div>
              <span className="font-medium">联系电话:</span> {mockTask.contactPhone}
            </div>
          </div>
        </div>

        <div className="text-center">
          <Button 
            onClick={handleOpenModal}
            size="lg"
            className="px-8 py-3"
          >
            <QrCode className="mr-2 h-5 w-5" />
            生成任务二维码
          </Button>
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">功能说明</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 点击按钮将打开二维码模态框</li>
            <li>• 二维码内容格式：任务编号@工程名称</li>
            <li>• 支持复制二维码内容到剪贴板</li>
            <li>• 支持下载二维码图片</li>
            <li>• 支持打印二维码</li>
          </ul>
        </div>

        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-800 mb-2">预期二维码内容</h3>
          <p className="text-green-700 font-mono text-sm">
            {mockTask.taskNumber}@{mockTask.projectName}
          </p>
        </div>
      </div>

      {/* 二维码模态框 */}
      <QRCodeModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        task={mockTask}
      />
    </div>
  );
}
