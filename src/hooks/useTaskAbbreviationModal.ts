// src/hooks/useTaskAbbreviationModal.ts
'use client';

import { useState, useCallback } from 'react';
import type { Task } from '@/types';
import { TaskAbbreviationService } from '@/services/taskAbbreviationService';

interface UseTaskAbbreviationModalReturn {
  // Task Abbreviation Modal state
  isTaskAbbreviationModalOpen: boolean;
  selectedTaskForAbbreviation: Task | null;
  openTaskAbbreviationModal: (task: Task) => void;
  closeTaskAbbreviationModal: () => void;
  handleSaveAbbreviation: (taskId: string, abbreviation: string) => Promise<void>;
}

export function useTaskAbbreviationModal(): UseTaskAbbreviationModalReturn {
  const [isTaskAbbreviationModalOpen, setIsTaskAbbreviationModalOpen] = useState(false);
  const [selectedTaskForAbbreviation, setSelectedTaskForAbbreviation] = useState<Task | null>(null);

  const openTaskAbbreviationModal = useCallback((task: Task) => {
    setSelectedTaskForAbbreviation(task);
    setIsTaskAbbreviationModalOpen(true);
  }, []);

  const closeTaskAbbreviationModal = useCallback(() => {
    setIsTaskAbbreviationModalOpen(false);
    setSelectedTaskForAbbreviation(null); // Clear selected task when closing
  }, []);

  const handleSaveAbbreviation = useCallback(async (taskId: string, abbreviation: string) => {
    await TaskAbbreviationService.updateAbbreviation(taskId, abbreviation);
  }, []);

  return {
    isTaskAbbreviationModalOpen,
    selectedTaskForAbbreviation,
    openTaskAbbreviationModal,
    closeTaskAbbreviationModal,
    handleSaveAbbreviation,
  };
}
