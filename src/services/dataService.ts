
import type { Plant, Task, DeliveryOrder, Vehicle } from '@/types';
import { httpClient } from './httpClient';
import { DataAdapters } from './dataAdapters';
import { getApiConfig } from '@/config/api';

// 获取任务数据
export async function getTasks(): Promise<Task[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.tasks);

    // 如果是mock数据，直接返回（httpClient已处理）
    if (config.baseUrl === '') {
      return response as Task[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptTasks(response);
  } catch (error) {
    console.error('Failed to fetch tasks:', error);
    throw error;
  }
}

// 获取车辆数据
export async function getVehicles(): Promise<Vehicle[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.vehicles);

    // 如果是mock数据，直接返回
    if (config.baseUrl === '') {
      return response as Vehicle[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptVehicles(response);
  } catch (error) {
    console.error('Failed to fetch vehicles:', error);
    throw error;
  }
}

// 获取配送单数据
export async function getDeliveryOrders(): Promise<DeliveryOrder[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.deliveryOrders);

    // 如果是mock数据，直接返回
    if (config.baseUrl === '') {
      return response as DeliveryOrder[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptDeliveryOrders(response);
  } catch (error) {
    console.error('Failed to fetch delivery orders:', error);
    throw error;
  }
}

// 获取搅拌站数据
export async function getStaticPlants(): Promise<Plant[]> {
  try {
    const config = getApiConfig();
    const response = await httpClient.get<any[]>(config.endpoints.plants);

    // 如果是mock数据，直接返回
    if (config.baseUrl === '') {
      return response as Plant[];
    }

    // 真实API数据需要适配
    return DataAdapters.adaptPlants(response);
  } catch (error) {
    console.error('Failed to fetch plants:', error);
    throw error;
  }
}

// 车辆调度到任务
export async function dispatchVehicleToTaskService(
  currentVehicles: Vehicle[],
  vehicleId: string,
  taskId: string,
  productionLineId: string
): Promise<Vehicle | null> {
  try {
    const config = getApiConfig();

    // 如果是mock模式，使用原有逻辑
    if (config.baseUrl === '') {
      await new Promise(resolve => setTimeout(resolve, 50));
      const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
      if (vehicleIndex > -1) {
        const updatedVehicle = {
          ...currentVehicles[vehicleIndex],
          status: 'outbound' as Vehicle['status'],
          assignedTaskId: taskId,
          assignedProductionLineId: productionLineId,
          productionStatus: 'queued' as Vehicle['productionStatus'],
        };
        return updatedVehicle;
      }
      console.error(`dispatchVehicleToTaskService: Vehicle ${vehicleId} not found.`);
      return null;
    }

    // 真实API调用
    const response = await httpClient.patch<any>(`/vehicles/${vehicleId}/dispatch`, {
      taskId,
      productionLineId,
    });

    return DataAdapters.adaptVehicle(response);
  } catch (error) {
    console.error('Failed to dispatch vehicle:', error);
    throw error;
  }
}

export async function cancelVehicleDispatchService(
  currentVehicles: Vehicle[],
  vehicleId: string
): Promise<Vehicle | null> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
  if (vehicleIndex > -1) {
    const updatedVehicle = {
      ...currentVehicles[vehicleIndex],
      status: 'pending' as Vehicle['status'], // Or determine original status if tracked
      assignedTaskId: undefined,
      assignedProductionLineId: undefined,
      productionStatus: undefined,
      currentTripType: undefined,
      deliveryOrderId: undefined,
    };
    return updatedVehicle;
  }
  console.error(`cancelVehicleDispatchService: Vehicle ${vehicleId} not found.`);
  return null;
}

export async function reorderVehiclesInListService(
  currentVehicles: Vehicle[],
  draggedVehicleId: string,
  targetVehicleId: string,
  listStatus: Vehicle['status']
): Promise<Vehicle[]> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehiclesCopy = [...currentVehicles];
  const listToReorder = vehiclesCopy.filter(v => v.status === listStatus);
  const otherVehicles = vehiclesCopy.filter(v => v.status !== listStatus);

  const draggedItem = listToReorder.find(v => v.id === draggedVehicleId);
  if (!draggedItem) return currentVehicles; // Should not happen

  const remainingItems = listToReorder.filter(v => v.id !== draggedVehicleId);
  const targetIndex = remainingItems.findIndex(v => v.id === targetVehicleId);

  if (targetIndex === -1) { // Drop on the list but not on a specific card (e.g. end of list)
      remainingItems.push(draggedItem);
  } else {
      remainingItems.splice(targetIndex, 0, draggedItem);
  }
  
  return [...otherVehicles, ...remainingItems];
}

export async function confirmCrossPlantDispatchService(
  currentVehicles: Vehicle[],
  vehicleId: string,
  targetPlantId: string,
  notes?: string 
): Promise<Vehicle | null> {
  await new Promise(resolve => setTimeout(resolve, 50));
  const vehicleIndex = currentVehicles.findIndex(v => v.id === vehicleId);
  if (vehicleIndex > -1) {
    const updatedVehicle = {
      ...currentVehicles[vehicleIndex],
      plantId: targetPlantId,
      status: 'pending' as Vehicle['status'], // Assuming it becomes pending in the new plant
      assignedTaskId: undefined,
      assignedProductionLineId: undefined,
      // Potentially log notes or handle other cross-plant logic here
    };
    console.log(`Cross-plant dispatch: Vehicle ${vehicleId} to Plant ${targetPlantId}. Notes: ${notes || 'N/A'}`);
    return updatedVehicle;
  }
  console.error(`confirmCrossPlantDispatchService: Vehicle ${vehicleId} not found.`);
  return null;
}
