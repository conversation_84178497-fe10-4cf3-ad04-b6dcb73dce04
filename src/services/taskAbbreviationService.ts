// src/services/taskAbbreviationService.ts

import { useAppStore } from '@/store/appStore';

/**
 * 更新任务缩写的API服务
 */
export class TaskAbbreviationService {
  private static readonly API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://47.107.119.236:7033';
  
  /**
   * 更新任务缩写
   * @param taskId 任务ID
   * @param abbreviation 新的缩写
   * @returns Promise<void>
   */
  static async updateTaskAbbreviation(taskId: string, abbreviation: string): Promise<void> {
    try {
      // 获取认证信息
      const authParams = {
        redisToken: 'your_token_here', // 实际项目中应该从认证状态获取
        u_n: '系统管理员',
        w_n: 'yulei-admin'
      };

      const response = await fetch(`${this.API_BASE_URL}/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...authParams,
          task: {
            projectAbbreviation: abbreviation
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || '更新失败');
      }

      // 更新本地状态
      const updateTask = useAppStore.getState().updateTask;
      updateTask(taskId, { projectAbbreviation: abbreviation });

    } catch (error) {
      console.error('更新任务缩写失败:', error);
      throw error;
    }
  }

  /**
   * Mock版本的更新任务缩写（用于开发测试）
   * @param taskId 任务ID
   * @param abbreviation 新的缩写
   * @returns Promise<void>
   */
  static async updateTaskAbbreviationMock(taskId: string, abbreviation: string): Promise<void> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟随机失败（10%概率）
    if (Math.random() < 0.1) {
      throw new Error('模拟网络错误');
    }

    // 更新本地状态
    const updateTask = useAppStore.getState().updateTask;
    updateTask(taskId, { projectAbbreviation: abbreviation });
  }

  /**
   * 根据环境选择使用真实API还是Mock
   * @param taskId 任务ID
   * @param abbreviation 新的缩写
   * @returns Promise<void>
   */
  static async updateAbbreviation(taskId: string, abbreviation: string): Promise<void> {
    const useMockData = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';
    
    if (useMockData) {
      return this.updateTaskAbbreviationMock(taskId, abbreviation);
    } else {
      return this.updateTaskAbbreviation(taskId, abbreviation);
    }
  }
}
