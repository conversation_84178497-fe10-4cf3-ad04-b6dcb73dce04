import { Task, ReminderType, ReminderMessage, ReminderConfig } from '@/types';
import { playReminderSound } from '@/utils/playSound';
import { useToast } from '@/hooks/use-toast';

/**
 * 检查任务是否需要提醒
 * @param task 任务对象
 * @param config 提醒配置
 * @returns 是否需要提醒
 */
export const shouldTriggerReminder = (task: Task, config: ReminderConfig): boolean => {
  // 如果配置被禁用或任务不在进行中状态，则不触发提醒
  if (!config.enabled || task.dispatchStatus !== 'InProgress') {
    return false;
  }

  // 如果任务被标记为需要提醒
  return task.isDueForDispatch === true;
};

/**
 * 根据时间差获取应用的提醒级别
 * @param task 任务对象
 * @param config 提醒配置
 * @returns 匹配的提醒级别，如果没有匹配则返回null
 */
export const getActiveReminderLevel = (task: Task, config: ReminderConfig): {minutes: number, types: ReminderType[]} | null => {
  if (!task.minutesToDispatch || !config.reminderLevels || config.reminderLevels.length === 0) {
    return null;
  }
  
  // 找出时间差最接近当前时间的提醒级别
  for (const level of config.reminderLevels) {
    if (task.minutesToDispatch <= level.minutes && task.minutesToDispatch > 0) {
      return level;
    }
  }
  
  return null;
};

/**
 * 触发任务提醒
 * @param task 任务对象
 * @param config 提醒配置
 */
export const triggerTaskReminder = async (task: Task, config: ReminderConfig): Promise<void> => {
  if (!config.enabled) return;

  const toast = useToast();
  
  try {
    // 获取匹配的提醒级别
    const reminderLevel = getActiveReminderLevel(task, config);
    const reminderTypes = reminderLevel?.types || config.reminderTypes;
    
    // 如果没有提醒类型，则不触发提醒
    if (!reminderTypes || reminderTypes.length === 0) return;
    
    // 构建基础消息
    const baseMessage: Omit<ReminderMessage, 'id' | 'time' | 'read'> = {
      taskId: task.id,
      taskNumber: task.taskNumber,
      title: '发车提醒',
      description: `任务 ${task.taskNumber}（${task.projectName}）${
        task.minutesToDispatch && task.minutesToDispatch > 0 
          ? `距离发车还有${task.minutesToDispatch}分钟` 
          : '即将发车'
      }`,
      type: 'dispatchCountdown',
      projectName: task.projectName
    };
    
    // 添加消息提醒
    // reminderStore.addMessage(baseMessage);
    
    // 如果配置了声音提醒，播放提醒音效
    if (reminderTypes.includes('sound')) {
      try {
        await playReminderSound();
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error('播放提醒音效失败:', err);
        
        // 添加错误消息
        // reminderStore.addErrorMessage(err, '播放提醒音效');
        
        // 显示Toast提示
        toast.toast({
          title: '声音提醒失败',
          description: err.message,
          variant: 'destructive'
        });
      }
    }
    
    // 如果配置了弹窗提醒，显示Toast通知
    if (reminderTypes.includes('popup')) {
      toast.toast({
        title: '发车提醒',
        description: baseMessage.description,
        variant: 'default'
      });
    }
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    console.error('触发提醒失败:', err);
    
    // 添加错误消息
    // reminderStore.addErrorMessage(err, '触发提醒');
  }
};

/**
 * 获取任务的提醒配置
 * @param taskId 任务ID
 * @returns 提醒配置或undefined
 */
export const getTaskReminderConfig = (taskId: string): ReminderConfig | undefined => {
  // const { configs } = useReminderStore.getState();
  return undefined;
};

/**
 * 处理任务提醒
 * @param tasks 任务列表
 */
export const processTaskReminders = (tasks: Task[]): void => {
  try {
    // 获取已配置的任务
    const remindableTasks = tasks.filter(task => {
      // 只处理进行中的任务
      if (task.dispatchStatus !== 'InProgress') return false;
      
      // 检查是否有提醒配置
      const config = getTaskReminderConfig(task.id);
      return config?.enabled === true;
    });
    
    // 遍历处理每个任务
    remindableTasks.forEach(task => {
      const config = getTaskReminderConfig(task.id);
      if (config && shouldTriggerReminder(task, config)) {
        triggerTaskReminder(task, config);
      }
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    console.error('处理任务提醒失败:', err);
    
    // 添加错误消息
    // const reminderStore = useReminderStore.getState();
    // reminderStore.addErrorMessage(err, '处理任务提醒');
  }
}; 