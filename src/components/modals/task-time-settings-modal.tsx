'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Clock } from 'lucide-react';
import type { Task } from '@/types';

interface TaskTimeSettingsModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
  onSave: (taskId: string, settings: TimeSettings) => void;
}

interface TimeSettings {
  supplyDate: string;
  supplyTime: string;
  dispatchFrequencyMinutes: number;
}

export function TaskTimeSettingsModal({
  isOpen,
  onOpenChange,
  task,
  onSave,
}: TaskTimeSettingsModalProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [supplyTime, setSupplyTime] = useState('');
  const [dispatchFrequency, setDispatchFrequency] = useState(30);

  // 初始化表单数据
  useEffect(() => {
    if (task && isOpen) {
      // 解析现有的供货日期
      if (task.supplyDate) {
        try {
          const date = new Date(task.supplyDate);
          if (!isNaN(date.getTime())) {
            setSelectedDate(date);
          }
        } catch (error) {
          console.warn('Invalid supply date:', task.supplyDate);
        }
      }
      
      // 设置供货时间
      setSupplyTime(task.supplyTime || '');
      
      // 设置发车间隔
      setDispatchFrequency(task.dispatchFrequencyMinutes || 30);
    }
  }, [task, isOpen]);

  const handleSave = () => {
    if (!task) return;

    const settings: TimeSettings = {
      supplyDate: selectedDate ? format(selectedDate, 'yyyy-MM-dd') : '',
      supplyTime,
      dispatchFrequencyMinutes: dispatchFrequency,
    };

    onSave(task.id, settings);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设定时间 - {task.taskNumber}</DialogTitle>
          <DialogDescription>
            设置任务的计划供货时间和发车间隔提醒。
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 计划供货日期 */}
          <div className="grid gap-2">
            <Label htmlFor="supply-date">计划供货日期</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    !selectedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, 'yyyy-MM-dd') : '选择日期'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* 计划供货时间 */}
          <div className="grid gap-2">
            <Label htmlFor="supply-time">计划供货时间</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="supply-time"
                type="time"
                value={supplyTime}
                onChange={(e) => setSupplyTime(e.target.value)}
                className="pl-10"
                placeholder="HH:MM"
              />
            </div>
          </div>

          {/* 发车间隔设置 */}
          <div className="grid gap-2">
            <Label htmlFor="dispatch-frequency">发车间隔（分钟）</Label>
            <Input
              id="dispatch-frequency"
              type="number"
              min="1"
              max="1440"
              value={dispatchFrequency}
              onChange={(e) => setDispatchFrequency(parseInt(e.target.value) || 30)}
              placeholder="30"
            />
            <p className="text-xs text-muted-foreground">
              设置发车提醒的间隔时间，用于计算下次发车提醒
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleSave}>
            保存设置
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
