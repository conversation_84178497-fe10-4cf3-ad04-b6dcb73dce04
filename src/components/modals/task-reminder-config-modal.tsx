'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useReminderStore, DEFAULT_REMINDER_LEVELS } from '@/store/reminderStore';
import { ReminderType, Task, ReminderConfig } from '@/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash2, Clock, <PERSON>, Zap } from 'lucide-react';

interface TaskReminderConfigModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  task: Task | null;
}

interface ReminderTypeOption {
  value: ReminderType;
  label: string;
  description: string;
  icon: React.ReactNode;
}

const reminderTypeOptions: ReminderTypeOption[] = [
  {
    value: 'dispatchCountdown',
    label: '发车倒计时',
    description: '显示距离下次发车的倒计时',
    icon: <Clock className="h-4 w-4" />
  },
  {
    value: 'highlight',
    label: '高亮提醒',
    description: '在任务列表中高亮显示需要发车的任务',
    icon: <Zap className="h-4 w-4" />
  },
  {
    value: 'popup',
    label: '弹窗提醒',
    description: '在发车时弹出提醒窗口',
    icon: <Bell className="h-4 w-4" />
  },
  {
    value: 'sound',
    label: '声音提醒',
    description: '发车时播放提醒音效',
    icon: <Bell className="h-4 w-4" />
  }
];

export function TaskReminderConfigModal({
  isOpen,
  onOpenChange,
  task
}: TaskReminderConfigModalProps) {
  // 从store获取配置操作方法
  const { configs, addConfig, updateConfig } = useReminderStore();
  
  // 使用useMemo创建默认配置，避免每次渲染重新创建
  const defaultConfig = useMemo<ReminderConfig>(() => ({
    enabled: true,
    taskId: task?.id || '',
    reminderTypes: ['highlight' as ReminderType, 'dispatchCountdown' as ReminderType],
    reminderFrequencyMinutes: 15,
    reminderLevels: DEFAULT_REMINDER_LEVELS,
    defaultDispatchFrequencyMinutes: 60, // 默认每小时发车一次
    customSettings: {}
  }), [task?.id]);
  
  // 本地状态
  const [formState, setFormState] = useState<ReminderConfig>(defaultConfig);
  const [activeTab, setActiveTab] = useState<string>("general");
  
  // 初始化表单状态
  useEffect(() => {
    if (!task) return;
    
    const existingConfig = configs.find(c => c.taskId === task.id);
    if (existingConfig) {
      // 确保现有配置有所有必要的字段
      setFormState({
        ...existingConfig,
        reminderLevels: existingConfig.reminderLevels || DEFAULT_REMINDER_LEVELS,
        defaultDispatchFrequencyMinutes: existingConfig.defaultDispatchFrequencyMinutes || 60
      });
    } else {
      setFormState({
        ...defaultConfig,
        taskId: task.id
      });
    }
  }, [task, configs]); // 移除defaultConfig依赖
  
  // 表单变更处理
  const handleEnabledChange = (checked: boolean) => {
    setFormState(prev => ({ ...prev, enabled: checked }));
  };
  
  const handleFrequencyChange = (value: string) => {
    setFormState(prev => ({ 
      ...prev, 
      reminderFrequencyMinutes: parseInt(value, 10) 
    }));
  };
  
  const handleDefaultDispatchFrequencyChange = (value: string) => {
    setFormState(prev => ({ 
      ...prev, 
      defaultDispatchFrequencyMinutes: parseInt(value, 10) 
    }));
  };
  
  const handleReminderTypeChange = (type: ReminderType, checked: boolean) => {
    setFormState(prev => {
      const types = checked 
        ? [...prev.reminderTypes, type] 
        : prev.reminderTypes.filter(t => t !== type);
      
      return { ...prev, reminderTypes: types };
    });
  };

  // 更新提醒级别
  const updateReminderLevel = (index: number, field: 'minutes' | 'types', value: any) => {
    setFormState(prev => {
      const newLevels = [...prev.reminderLevels];
      
      if (field === 'types') {
        const type = value.type as ReminderType;
        const checked = value.checked as boolean;
        const currentTypes = newLevels[index].types;
        
        newLevels[index] = {
          ...newLevels[index],
          types: checked
            ? [...currentTypes, type]
            : currentTypes.filter(t => t !== type)
        };
      } else {
        newLevels[index] = {
          ...newLevels[index],
          [field]: value
        };
      }
      
      return { ...prev, reminderLevels: newLevels };
    });
  };
  
  // 添加新的提醒级别
  const addReminderLevel = () => {
    setFormState(prev => ({
      ...prev,
      reminderLevels: [
        ...prev.reminderLevels,
        { minutes: 10, types: ['dispatchCountdown' as ReminderType] }
      ]
    }));
  };
  
  // 删除提醒级别
  const removeReminderLevel = (index: number) => {
    setFormState(prev => ({
      ...prev,
      reminderLevels: prev.reminderLevels.filter((_, i) => i !== index)
    }));
  };
  
  // 保存配置
  const handleSave = () => {
    if (task) {
      const existingConfig = configs.find(c => c.taskId === task.id);
      if (existingConfig) {
        updateConfig(task.id, formState);
      } else {
        addConfig(formState);
      }
      onOpenChange(false);
    }
  };
  
  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>任务提醒设置</DialogTitle>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="general">基本设置</TabsTrigger>
            <TabsTrigger value="levels">多级提醒</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-4 py-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="reminder-enabled">启用提醒</Label>
                <p className="text-xs text-muted-foreground">开启后将按照设置的方式提醒</p>
              </div>
              <Switch
                id="reminder-enabled"
                checked={formState.enabled}
                onCheckedChange={handleEnabledChange}
              />
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Label>提醒方式</Label>
              <div className="grid gap-2">
                {reminderTypeOptions.map(option => (
                  <div key={option.value} className="flex items-start space-x-2">
                    <Checkbox
                      id={`reminder-type-${option.value}`}
                      checked={formState.reminderTypes.includes(option.value)}
                      onCheckedChange={(checked) => 
                        handleReminderTypeChange(option.value, checked === true)
                      }
                      disabled={!formState.enabled}
                    />
                    <div className="grid gap-0.5 leading-none">
                      <div className="flex items-center space-x-1">
                        {option.icon}
                        <Label
                          htmlFor={`reminder-type-${option.value}`}
                          className="text-sm cursor-pointer"
                        >
                          {option.label}
                        </Label>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {option.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Label htmlFor="default-dispatch-frequency">默认发车频率（分钟）</Label>
              <Select
                disabled={!formState.enabled}
                value={formState.defaultDispatchFrequencyMinutes?.toString() || "60"}
                onValueChange={handleDefaultDispatchFrequencyChange}
              >
                <SelectTrigger id="default-dispatch-frequency">
                  <SelectValue placeholder="选择默认发车频率" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>默认发车频率</SelectLabel>
                    <SelectItem value="15">15分钟</SelectItem>
                    <SelectItem value="30">30分钟</SelectItem>
                    <SelectItem value="45">45分钟</SelectItem>
                    <SelectItem value="60">1小时</SelectItem>
                    <SelectItem value="90">1.5小时</SelectItem>
                    <SelectItem value="120">2小时</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                设置任务默认的发车频率（当任务未指定时使用）
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="reminder-frequency">提醒频率（分钟）</Label>
              <Select
                disabled={!formState.enabled}
                value={formState.reminderFrequencyMinutes.toString()}
                onValueChange={handleFrequencyChange}
              >
                <SelectTrigger id="reminder-frequency">
                  <SelectValue placeholder="选择提醒频率" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>提醒频率</SelectLabel>
                    <SelectItem value="5">5分钟</SelectItem>
                    <SelectItem value="10">10分钟</SelectItem>
                    <SelectItem value="15">15分钟</SelectItem>
                    <SelectItem value="30">30分钟</SelectItem>
                    <SelectItem value="60">1小时</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                设置每隔多长时间检查一次提醒
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="levels" className="space-y-4 py-4">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">多级提醒设置</Label>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={addReminderLevel}
                disabled={!formState.enabled}
              >
                <Plus className="h-4 w-4 mr-1" /> 添加级别
              </Button>
            </div>
            
            <p className="text-xs text-muted-foreground">
              设置在不同时间点触发不同类型的提醒
            </p>
            
            <div className="space-y-4">
              {formState.reminderLevels.map((level, index) => (
                <div key={index} className="border rounded-md p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                      <Label className="text-sm font-medium">
                        提前 {level.minutes} 分钟
                      </Label>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Select
                        disabled={!formState.enabled}
                        value={level.minutes.toString()}
                        onValueChange={(value) => 
                          updateReminderLevel(index, 'minutes', parseInt(value))
                        }
                      >
                        <SelectTrigger className="w-[100px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">5分钟</SelectItem>
                          <SelectItem value="10">10分钟</SelectItem>
                          <SelectItem value="15">15分钟</SelectItem>
                          <SelectItem value="30">30分钟</SelectItem>
                          <SelectItem value="60">1小时</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Button
                        variant="ghost"
                        size="icon"
                        disabled={formState.reminderLevels.length <= 1 || !formState.enabled}
                        onClick={() => removeReminderLevel(index)}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    {reminderTypeOptions.map(option => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`level-${index}-${option.value}`}
                          checked={level.types.includes(option.value)}
                          onCheckedChange={(checked) => 
                            updateReminderLevel(index, 'types', {
                              type: option.value,
                              checked: checked === true
                            })
                          }
                          disabled={!formState.enabled}
                        />
                        <div className="flex items-center space-x-1">
                          {option.icon}
                          <Label
                            htmlFor={`level-${index}-${option.value}`}
                            className="text-xs cursor-pointer"
                          >
                            {option.label}
                          </Label>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSave}>
            保存设置
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}