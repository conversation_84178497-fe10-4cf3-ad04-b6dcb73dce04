
'use client';

import type { Task } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface TankerDispatchNoteModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
}

export function TankerDispatchNoteModal({ isOpen, onOpenChange, task }: TankerDispatchNoteModalProps) {
  if (!task) return null;

  // Placeholder for form state and handling
  const handleSubmit = () => {
    console.log('Submitting tanker dispatch note for task:', task.id);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>罐车发车单 - {task.taskNumber}</DialogTitle>
          <DialogDescription>
            查看和编辑任务 “{task.projectName}” 的罐车发车信息。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="task-project" className="text-right">
              工程名称
            </Label>
            <Input id="task-project" value={task.projectName} readOnly className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="task-strength" className="text-right">
              强度
            </Label>
            <Input id="task-strength" value={task.strength} readOnly className="col-span-3" />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="task-required-volume" className="text-right">
              需供量 (m³)
            </Label>
            <Input id="task-required-volume" value={task.requiredVolume} readOnly className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="dispatch-notes" className="text-right pt-2">
              发车备注
            </Label>
            <Textarea id="dispatch-notes" placeholder="输入发车备注..." className="col-span-3 min-h-[80px]" />
          </div>
          {/* Add more fields as needed for editing */}
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button type="submit" onClick={handleSubmit}>
            保存更改
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
