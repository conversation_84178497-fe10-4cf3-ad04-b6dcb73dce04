'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FlaskConical, Package, Droplets, Mountain, 
  Zap, Calculator, AlertTriangle, Save 
} from 'lucide-react';
import type { Task } from '@/types';

interface RatioEditModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
  onSave: (taskId: string, ratio: ConcreteRatio) => void;
}

// 混凝土配比接口
interface ConcreteRatio {
  strength: string;
  cement: number;      // 水泥 kg/m³
  water: number;       // 水 kg/m³
  sand: number;        // 砂 kg/m³
  gravel: number;      // 石子 kg/m³
  additive?: number;   // 外加剂 kg/m³
  slump: string;       // 坍落度
  waterCementRatio: number; // 水灰比
}

export function RatioEditModal({
  isOpen,
  onOpenChange,
  task,
  onSave,
}: RatioEditModalProps) {
  const [ratio, setRatio] = useState<ConcreteRatio>({
    strength: 'C25',
    cement: 372,
    water: 175,
    sand: 593,
    gravel: 1260,
    additive: 3.72,
    slump: '180±20mm',
    waterCementRatio: 0.47,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 初始化配比数据
  useEffect(() => {
    if (task && isOpen) {
      // 根据任务强度等级设置默认配比
      const defaultRatios: Record<string, ConcreteRatio> = {
        'C25': {
          strength: 'C25',
          cement: 372,
          water: 175,
          sand: 593,
          gravel: 1260,
          additive: 3.72,
          slump: '180±20mm',
          waterCementRatio: 0.47,
        },
        'C30': {
          strength: 'C30',
          cement: 425,
          water: 175,
          sand: 583,
          gravel: 1240,
          additive: 4.25,
          slump: '180±20mm',
          waterCementRatio: 0.41,
        },
        'C35': {
          strength: 'C35',
          cement: 461,
          water: 175,
          sand: 571,
          gravel: 1220,
          additive: 4.61,
          slump: '180±20mm',
          waterCementRatio: 0.38,
        },
      };

      const defaultRatio = defaultRatios[task.strength] || defaultRatios['C25'];
      setRatio({ ...defaultRatio, strength: task.strength });
      setErrors({});
    }
  }, [task, isOpen]);

  // 验证配比数据
  const validateRatio = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (ratio.cement <= 0) newErrors.cement = '水泥用量必须大于0';
    if (ratio.water <= 0) newErrors.water = '水用量必须大于0';
    if (ratio.sand <= 0) newErrors.sand = '砂用量必须大于0';
    if (ratio.gravel <= 0) newErrors.gravel = '石子用量必须大于0';
    
    // 检查水灰比合理性
    const calculatedWaterCementRatio = ratio.water / ratio.cement;
    if (calculatedWaterCementRatio < 0.3 || calculatedWaterCementRatio > 0.7) {
      newErrors.waterCementRatio = '水灰比应在0.3-0.7之间';
    }

    // 检查总用量合理性
    const totalWeight = ratio.cement + ratio.water + ratio.sand + ratio.gravel + (ratio.additive || 0);
    if (totalWeight < 2000 || totalWeight > 2600) {
      newErrors.total = '总用量应在2000-2600 kg/m³之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 自动计算水灰比
  const updateWaterCementRatio = () => {
    if (ratio.cement > 0) {
      const newRatio = ratio.water / ratio.cement;
      setRatio(prev => ({ ...prev, waterCementRatio: Math.round(newRatio * 100) / 100 }));
    }
  };

  const handleInputChange = (field: keyof ConcreteRatio, value: string | number) => {
    setRatio(prev => ({ ...prev, [field]: value }));
    
    // 如果修改了水泥或水的用量，自动更新水灰比
    if (field === 'cement' || field === 'water') {
      setTimeout(updateWaterCementRatio, 0);
    }
  };

  const handleSave = () => {
    if (!task || !validateRatio()) return;
    
    onSave(task.id, ratio);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  if (!task) return null;

  const totalWeight = ratio.cement + ratio.water + ratio.sand + ratio.gravel + (ratio.additive || 0);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FlaskConical className="h-5 w-5" />
            修改配比 - {task.taskNumber}
          </DialogTitle>
          <DialogDescription>
            修改任务 {task.projectName} 的混凝土配比参数
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 基本信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Package className="h-4 w-4" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="strength">强度等级</Label>
                <div className="mt-1">
                  <Badge variant="secondary">{ratio.strength}</Badge>
                </div>
              </div>
              <div>
                <Label htmlFor="slump">坍落度</Label>
                <Input
                  id="slump"
                  value={ratio.slump}
                  onChange={(e) => handleInputChange('slump', e.target.value)}
                  placeholder="180±20mm"
                />
              </div>
            </CardContent>
          </Card>

          {/* 配比参数 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">配比参数（kg/m³）</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="cement" className="flex items-center gap-2">
                    <Package className="h-3 w-3" />
                    水泥
                  </Label>
                  <Input
                    id="cement"
                    type="number"
                    min="0"
                    step="1"
                    value={ratio.cement}
                    onChange={(e) => handleInputChange('cement', parseFloat(e.target.value) || 0)}
                    className={errors.cement ? 'border-red-500' : ''}
                  />
                  {errors.cement && (
                    <p className="text-xs text-red-500 mt-1">{errors.cement}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="water" className="flex items-center gap-2">
                    <Droplets className="h-3 w-3" />
                    水
                  </Label>
                  <Input
                    id="water"
                    type="number"
                    min="0"
                    step="1"
                    value={ratio.water}
                    onChange={(e) => handleInputChange('water', parseFloat(e.target.value) || 0)}
                    className={errors.water ? 'border-red-500' : ''}
                  />
                  {errors.water && (
                    <p className="text-xs text-red-500 mt-1">{errors.water}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="sand" className="flex items-center gap-2">
                    <Mountain className="h-3 w-3" />
                    砂
                  </Label>
                  <Input
                    id="sand"
                    type="number"
                    min="0"
                    step="1"
                    value={ratio.sand}
                    onChange={(e) => handleInputChange('sand', parseFloat(e.target.value) || 0)}
                    className={errors.sand ? 'border-red-500' : ''}
                  />
                  {errors.sand && (
                    <p className="text-xs text-red-500 mt-1">{errors.sand}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="gravel" className="flex items-center gap-2">
                    <Mountain className="h-3 w-3" />
                    石子
                  </Label>
                  <Input
                    id="gravel"
                    type="number"
                    min="0"
                    step="1"
                    value={ratio.gravel}
                    onChange={(e) => handleInputChange('gravel', parseFloat(e.target.value) || 0)}
                    className={errors.gravel ? 'border-red-500' : ''}
                  />
                  {errors.gravel && (
                    <p className="text-xs text-red-500 mt-1">{errors.gravel}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="additive" className="flex items-center gap-2">
                    <Zap className="h-3 w-3" />
                    外加剂
                  </Label>
                  <Input
                    id="additive"
                    type="number"
                    min="0"
                    step="0.1"
                    value={ratio.additive || 0}
                    onChange={(e) => handleInputChange('additive', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div>
                  <Label htmlFor="waterCementRatio" className="flex items-center gap-2">
                    <Calculator className="h-3 w-3" />
                    水灰比
                  </Label>
                  <Input
                    id="waterCementRatio"
                    type="number"
                    min="0.3"
                    max="0.7"
                    step="0.01"
                    value={ratio.waterCementRatio}
                    onChange={(e) => handleInputChange('waterCementRatio', parseFloat(e.target.value) || 0)}
                    className={errors.waterCementRatio ? 'border-red-500' : ''}
                  />
                  {errors.waterCementRatio && (
                    <p className="text-xs text-red-500 mt-1">{errors.waterCementRatio}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 计算结果 */}
          <Card className="bg-blue-50">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Calculator className="h-4 w-4" />
                计算结果
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">总用量：</span>
                  <span className="font-medium ml-2">{Math.round(totalWeight)} kg/m³</span>
                </div>
                <div>
                  <span className="text-muted-foreground">实际水灰比：</span>
                  <span className="font-medium ml-2">{Math.round((ratio.water / ratio.cement) * 100) / 100}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">砂率：</span>
                  <span className="font-medium ml-2">
                    {Math.round((ratio.sand / (ratio.sand + ratio.gravel)) * 100)}%
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">外加剂掺量：</span>
                  <span className="font-medium ml-2">
                    {Math.round(((ratio.additive || 0) / ratio.cement) * 1000) / 10}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 错误提示 */}
          {(errors.total || errors.waterCementRatio) && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {errors.total && <div>{errors.total}</div>}
                {errors.waterCementRatio && <div>{errors.waterCementRatio}</div>}
              </AlertDescription>
            </Alert>
          )}

          {/* 用量预估 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">用量预估</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">剩余方量：</span>
                  <span className="font-medium ml-2">{task.requiredVolume - task.completedVolume} m³</span>
                </div>
                <div>
                  <span className="text-muted-foreground">预计水泥用量：</span>
                  <span className="font-medium ml-2">
                    {Math.round(ratio.cement * (task.requiredVolume - task.completedVolume))} kg
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={Object.keys(errors).length > 0}>
            <Save className="mr-2 h-4 w-4" />
            保存配比
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
