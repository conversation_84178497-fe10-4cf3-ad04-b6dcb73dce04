'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Truck, Clock, MapPin, User, Package, 
  TrendingUp, Calendar, Timer, BarChart3 
} from 'lucide-react';
import { format } from 'date-fns';
import type { Task } from '@/types';

interface DispatchDetailsModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
}

// 出车记录接口
interface DispatchRecord {
  id: string;
  vehicleNumber: string;
  driver: string;
  dispatchTime: string;
  returnTime?: string;
  volume: number;
  status: 'dispatched' | 'returned' | 'in_transit';
  productionLine: string;
  notes?: string;
}

export function DispatchDetailsModal({
  isOpen,
  onOpenChange,
  task,
}: DispatchDetailsModalProps) {
  // 模拟出车记录数据
  const getDispatchRecords = (taskId: string): DispatchRecord[] => {
    return [
      {
        id: '1',
        vehicleNumber: '京A12345',
        driver: '张师傅',
        dispatchTime: '2024-12-26 08:30:00',
        returnTime: '2024-12-26 10:15:00',
        volume: 8,
        status: 'returned',
        productionLine: '1号线',
        notes: '正常出车',
      },
      {
        id: '2',
        vehicleNumber: '京A67890',
        driver: '李师傅',
        dispatchTime: '2024-12-26 09:00:00',
        returnTime: '2024-12-26 11:30:00',
        volume: 10,
        status: 'returned',
        productionLine: '2号线',
      },
      {
        id: '3',
        vehicleNumber: '京A11111',
        driver: '王师傅',
        dispatchTime: '2024-12-26 10:30:00',
        volume: 8,
        status: 'in_transit',
        productionLine: '1号线',
      },
      {
        id: '4',
        vehicleNumber: '京A22222',
        driver: '赵师傅',
        dispatchTime: '2024-12-26 11:00:00',
        volume: 12,
        status: 'dispatched',
        productionLine: '3号线',
      },
    ];
  };

  if (!task) return null;

  const records = getDispatchRecords(task.id);
  const totalDispatched = records.length;
  const totalVolume = records.reduce((sum, record) => sum + record.volume, 0);
  const returnedCount = records.filter(r => r.status === 'returned').length;
  const inTransitCount = records.filter(r => r.status === 'in_transit').length;

  const getStatusBadge = (status: DispatchRecord['status']) => {
    switch (status) {
      case 'dispatched':
        return <Badge variant="secondary">已发车</Badge>;
      case 'in_transit':
        return <Badge variant="outline">运输中</Badge>;
      case 'returned':
        return <Badge variant="default">已返回</Badge>;
      default:
        return <Badge variant="secondary">未知</Badge>;
    }
  };

  const calculateDuration = (dispatchTime: string, returnTime?: string) => {
    if (!returnTime) return '-';
    
    const dispatch = new Date(dispatchTime);
    const returned = new Date(returnTime);
    const diffMinutes = Math.round((returned.getTime() - dispatch.getTime()) / (1000 * 60));
    
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            出车情况 - {task.taskNumber}
          </DialogTitle>
          <DialogDescription>
            查看任务 {task.projectName} 的详细出车记录和统计信息
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 统计概览 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">总出车数</p>
                    <p className="text-lg font-bold">{totalDispatched}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">总方量</p>
                    <p className="text-lg font-bold">{totalVolume} m³</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-orange-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">已返回</p>
                    <p className="text-lg font-bold">{returnedCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4 text-purple-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">运输中</p>
                    <p className="text-lg font-bold">{inTransitCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 出车记录列表 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">出车记录明细</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[300px]">
                <div className="space-y-3">
                  {records.map((record) => (
                    <div
                      key={record.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                    >
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Truck className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium text-sm">{record.vehicleNumber}</p>
                            <p className="text-xs text-muted-foreground flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {record.driver}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm">{record.productionLine}</p>
                            <p className="text-xs text-muted-foreground">{record.volume} m³</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm">
                              {format(new Date(record.dispatchTime), 'HH:mm')}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {record.returnTime ? format(new Date(record.returnTime), 'HH:mm') : '未返回'}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {calculateDuration(record.dispatchTime, record.returnTime)}
                          </p>
                          <p className="text-xs text-muted-foreground">用时</p>
                        </div>
                        {getStatusBadge(record.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* 进度信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">任务进度</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-xs text-muted-foreground">需求方量</p>
                  <p className="text-lg font-bold">{task.requiredVolume} m³</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">已完成方量</p>
                  <p className="text-lg font-bold text-green-600">{task.completedVolume} m³</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">完成进度</p>
                  <p className="text-lg font-bold text-blue-600">
                    {Math.round((task.completedVolume / task.requiredVolume) * 100)}%
                  </p>
                </div>
              </div>
              
              <div className="mt-3 w-full bg-muted rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all"
                  style={{
                    width: `${Math.min((task.completedVolume / task.requiredVolume) * 100, 100)}%`,
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
