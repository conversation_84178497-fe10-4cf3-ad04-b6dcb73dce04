'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, Package, Clock, TrendingUp, 
  CheckCircle, AlertCircle, Calendar, Target 
} from 'lucide-react';
import { format } from 'date-fns';
import type { Task } from '@/types';

interface TaskProgressModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
}

export function TaskProgressModal({
  isOpen,
  onOpenChange,
  task,
}: TaskProgressModalProps) {
  if (!task) return null;

  const progressPercentage = Math.round((task.completedVolume / task.requiredVolume) * 100);
  const remainingVolume = task.requiredVolume - task.completedVolume;
  const isCompleted = progressPercentage >= 100;
  const isOverdue = task.isDueForDispatch && !isCompleted;

  const getStatusInfo = () => {
    if (isCompleted) {
      return {
        icon: <CheckCircle className="h-5 w-5 text-green-600" />,
        text: '已完成',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
      };
    }
    if (isOverdue) {
      return {
        icon: <AlertCircle className="h-5 w-5 text-red-600" />,
        text: '超期',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
      };
    }
    return {
      icon: <Clock className="h-5 w-5 text-blue-600" />,
      text: '进行中',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            任务进度 - {task.taskNumber}
          </DialogTitle>
          <DialogDescription>
            查看任务 {task.projectName} 的详细进度信息
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 进度概览 */}
          <Card className={statusInfo.bgColor}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  {statusInfo.icon}
                  <div>
                    <h3 className="font-semibold">任务状态</h3>
                    <p className={`text-sm ${statusInfo.color}`}>{statusInfo.text}</p>
                  </div>
                </div>
                <Badge variant={isCompleted ? 'default' : isOverdue ? 'destructive' : 'secondary'}>
                  {progressPercentage}% 完成
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>进度</span>
                  <span>{task.completedVolume} / {task.requiredVolume} m³</span>
                </div>
                <Progress value={progressPercentage} className="h-3" />
              </div>
            </CardContent>
          </Card>

          {/* 详细统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">需求方量</p>
                    <p className="text-lg font-bold">{task.requiredVolume} m³</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">已完成</p>
                    <p className="text-lg font-bold text-green-600">{task.completedVolume} m³</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-orange-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">剩余方量</p>
                    <p className="text-lg font-bold text-orange-600">{remainingVolume} m³</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-purple-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">完成率</p>
                    <p className="text-lg font-bold text-purple-600">{progressPercentage}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 时间信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                时间信息
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-muted-foreground">计划供货时间</p>
                <p className="text-sm font-medium">
                  {task.supplyDate && task.supplyTime 
                    ? `${task.supplyDate} ${task.supplyTime}`
                    : '未设置'
                  }
                </p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">发布时间</p>
                <p className="text-sm font-medium">
                  {task.publishDate ? format(new Date(task.publishDate), 'yyyy-MM-dd HH:mm') : '未知'}
                </p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">发车间隔</p>
                <p className="text-sm font-medium">{task.dispatchFrequencyMinutes} 分钟</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">下次发车时间</p>
                <p className="text-sm font-medium">
                  {task.nextScheduledDispatchTime 
                    ? format(new Date(task.nextScheduledDispatchTime), 'HH:mm')
                    : '未计算'
                  }
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 任务详情 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">任务详情</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-muted-foreground">强度等级</p>
                <Badge variant="secondary" className="mt-1">{task.strength}</Badge>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">浇筑方式</p>
                <p className="text-sm font-medium">{task.pouringMethod || '未设置'}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">施工部位</p>
                <p className="text-sm font-medium">{task.constructionSite || '未设置'}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">联系电话</p>
                <p className="text-sm font-medium">{task.contactPhone || '未设置'}</p>
              </div>
            </CardContent>
          </Card>

          {/* 预计完成信息 */}
          {!isCompleted && (
            <Card className="bg-blue-50">
              <CardContent className="p-4">
                <h4 className="font-medium text-sm mb-2">预计完成信息</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">预计剩余车次：</span>
                    <span className="font-medium ml-2">
                      {Math.ceil(remainingVolume / 8)} 车次
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">预计完成时间：</span>
                    <span className="font-medium ml-2">
                      {task.estimatedDuration ? `${task.estimatedDuration} 小时` : '待计算'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
