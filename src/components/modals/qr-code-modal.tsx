'use client';

import React, { useState, useEffect } from 'react';
import QRCode from 'qrcode';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { QrCode, Download, Copy, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { Task } from '@/types';

interface QRCodeModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  task: Task | null;
}

export const QRCodeModal: React.FC<QRCodeModalProps> = ({
  isOpen,
  onOpenChange,
  task,
}) => {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  // 生成二维码内容
  const generateQRContent = (task: Task): string => {
    return `${task.taskNumber}@${task.projectName}`;
  };

  // 生成二维码
  const generateQRCode = async (content: string) => {
    try {
      setIsGenerating(true);
      const dataUrl = await QRCode.toDataURL(content, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
        errorCorrectionLevel: 'M',
      });
      setQrCodeDataUrl(dataUrl);
    } catch (error) {
      console.error('生成二维码失败:', error);
      toast({
        title: '生成失败',
        description: '二维码生成失败，请重试',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // 当任务变化时重新生成二维码
  useEffect(() => {
    if (task && isOpen) {
      const content = generateQRContent(task);
      generateQRCode(content);
    }
  }, [task, isOpen]);

  // 复制二维码内容到剪贴板
  const handleCopyContent = async () => {
    if (!task) return;
    
    try {
      const content = generateQRContent(task);
      await navigator.clipboard.writeText(content);
      setCopied(true);
      toast({
        title: '复制成功',
        description: '二维码内容已复制到剪贴板',
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制到剪贴板',
        variant: 'destructive',
      });
    }
  };

  // 下载二维码图片
  const handleDownload = () => {
    if (!qrCodeDataUrl || !task) return;

    try {
      const link = document.createElement('a');
      link.download = `任务二维码_${task.taskNumber}_${task.projectName}.png`;
      link.href = qrCodeDataUrl;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: '下载成功',
        description: '二维码图片已保存到本地',
      });
    } catch (error) {
      toast({
        title: '下载失败',
        description: '无法下载二维码图片',
        variant: 'destructive',
      });
    }
  };

  // 打印二维码
  const handlePrint = () => {
    if (!qrCodeDataUrl || !task) return;

    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast({
          title: '打印失败',
          description: '无法打开打印窗口，请检查浏览器设置',
          variant: 'destructive',
        });
        return;
      }

      const content = generateQRContent(task);
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>任务二维码 - ${task.taskNumber}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 20px;
                margin: 0;
              }
              .qr-container {
                display: inline-block;
                border: 2px solid #333;
                padding: 20px;
                border-radius: 8px;
                background: white;
              }
              .qr-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
              }
              .qr-content {
                font-size: 14px;
                color: #666;
                margin-bottom: 15px;
              }
              .qr-image {
                display: block;
                margin: 0 auto;
              }
              .qr-footer {
                font-size: 12px;
                color: #999;
                margin-top: 15px;
              }
              @media print {
                body { margin: 0; }
                .qr-container { border: 1px solid #333; }
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <div class="qr-title">任务二维码</div>
              <div class="qr-content">
                <div>任务编号：${task.taskNumber}</div>
                <div>工程名称：${task.projectName}</div>
              </div>
              <img src="${qrCodeDataUrl}" alt="任务二维码" class="qr-image" />
              <div class="qr-footer">
                内容：${content}<br/>
                生成时间：${new Date().toLocaleString()}
              </div>
            </div>
          </body>
        </html>
      `);
      
      printWindow.document.close();
      printWindow.focus();
      
      // 等待图片加载完成后打印
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
      
      toast({
        title: '打印任务已发送',
        description: '请检查打印机状态',
      });
    } catch (error) {
      toast({
        title: '打印失败',
        description: '无法执行打印操作',
        variant: 'destructive',
      });
    }
  };

  if (!task) return null;

  const qrContent = generateQRContent(task);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            任务二维码
          </DialogTitle>
          <DialogDescription>
            任务 {task.taskNumber} - {task.projectName} 的二维码
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex flex-col items-center space-y-4 py-4">
          {/* 二维码显示区域 */}
          <div className="relative">
            {isGenerating ? (
              <div className="w-[300px] h-[300px] flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-center">
                  <QrCode className="h-8 w-8 mx-auto mb-2 animate-pulse" />
                  <p className="text-sm text-gray-500">生成中...</p>
                </div>
              </div>
            ) : qrCodeDataUrl ? (
              <div className="border-2 border-gray-200 rounded-lg p-4 bg-white">
                <img 
                  src={qrCodeDataUrl} 
                  alt="任务二维码" 
                  className="w-[300px] h-[300px]"
                />
              </div>
            ) : (
              <div className="w-[300px] h-[300px] flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
                <p className="text-sm text-gray-500">无法生成二维码</p>
              </div>
            )}
          </div>

          {/* 二维码内容显示 */}
          <div className="text-center space-y-1">
            <p className="text-sm font-medium">二维码内容</p>
            <p className="text-xs text-gray-600 font-mono bg-gray-100 px-3 py-1 rounded">
              {qrContent}
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyContent}
              disabled={!task}
            >
              {copied ? (
                <Check className="h-4 w-4 mr-1" />
              ) : (
                <Copy className="h-4 w-4 mr-1" />
              )}
              {copied ? '已复制' : '复制内容'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              disabled={!qrCodeDataUrl}
            >
              <Download className="h-4 w-4 mr-1" />
              下载图片
            </Button>
            
            <Button
              variant="default"
              size="sm"
              onClick={handlePrint}
              disabled={!qrCodeDataUrl}
            >
              <QrCode className="h-4 w-4 mr-1" />
              打印二维码
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
