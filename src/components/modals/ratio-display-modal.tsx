'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TestTube, Droplets, Mountain, Zap, Package } from 'lucide-react';
import type { Task } from '@/types';

interface RatioDisplayModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
}

// 模拟配比数据接口
interface ConcreteRatio {
  strength: string;
  cement: number;      // 水泥 kg/m³
  water: number;       // 水 kg/m³
  sand: number;        // 砂 kg/m³
  gravel: number;      // 石子 kg/m³
  additive?: number;   // 外加剂 kg/m³
  slump: string;       // 坍落度
  waterCementRatio: number; // 水灰比
}

export function RatioDisplayModal({
  isOpen,
  onOpenChange,
  task,
}: RatioDisplayModalProps) {
  // 模拟获取配比数据
  const getRatioData = (strength: string): ConcreteRatio => {
    const ratioMap: Record<string, ConcreteRatio> = {
      'C25': {
        strength: 'C25',
        cement: 372,
        water: 175,
        sand: 593,
        gravel: 1260,
        additive: 3.72,
        slump: '180±20mm',
        waterCementRatio: 0.47,
      },
      'C30': {
        strength: 'C30',
        cement: 425,
        water: 175,
        sand: 583,
        gravel: 1240,
        additive: 4.25,
        slump: '180±20mm',
        waterCementRatio: 0.41,
      },
      'C35': {
        strength: 'C35',
        cement: 461,
        water: 175,
        sand: 571,
        gravel: 1220,
        additive: 4.61,
        slump: '180±20mm',
        waterCementRatio: 0.38,
      },
    };
    
    return ratioMap[strength] || ratioMap['C25'];
  };

  if (!task) return null;

  const ratioData = getRatioData(task.strength);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            混凝土配比 - {task.taskNumber}
          </DialogTitle>
          <DialogDescription>
            查看任务 {task.projectName} 的混凝土配比信息（只读模式）
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 基本信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Package className="h-4 w-4" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-muted-foreground">强度等级</p>
                <Badge variant="secondary" className="mt-1">
                  {ratioData.strength}
                </Badge>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">坍落度</p>
                <p className="text-sm font-medium mt-1">{ratioData.slump}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">水灰比</p>
                <p className="text-sm font-medium mt-1">{ratioData.waterCementRatio}</p>
              </div>
              <div>
                <p className="text-xs text-muted-foreground">浇筑方式</p>
                <p className="text-sm font-medium mt-1">{task.pouringMethod || '泵送'}</p>
              </div>
            </CardContent>
          </Card>

          {/* 配比详情 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">配比详情（kg/m³）</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="p-2 bg-slate-100 rounded">
                    <Package className="h-4 w-4 text-slate-600" />
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">水泥</p>
                    <p className="text-lg font-bold">{ratioData.cement}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="p-2 bg-blue-100 rounded">
                    <Droplets className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">水</p>
                    <p className="text-lg font-bold">{ratioData.water}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="p-2 bg-yellow-100 rounded">
                    <Mountain className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">砂</p>
                    <p className="text-lg font-bold">{ratioData.sand}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="p-2 bg-gray-100 rounded">
                    <Mountain className="h-4 w-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">石子</p>
                    <p className="text-lg font-bold">{ratioData.gravel}</p>
                  </div>
                </div>

                {ratioData.additive && (
                  <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <div className="p-2 bg-green-100 rounded">
                      <Zap className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">外加剂</p>
                      <p className="text-lg font-bold">{ratioData.additive}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 计算总量 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">用量计算</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-muted-foreground">需求方量</p>
                  <p className="text-lg font-bold">{task.requiredVolume} m³</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">已完成方量</p>
                  <p className="text-lg font-bold text-green-600">{task.completedVolume} m³</p>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-muted-foreground mb-2">剩余方量所需材料（按 {task.requiredVolume - task.completedVolume} m³ 计算）</p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                  <div>
                    <span className="text-muted-foreground">水泥：</span>
                    <span className="font-medium">{Math.round(ratioData.cement * (task.requiredVolume - task.completedVolume))} kg</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">水：</span>
                    <span className="font-medium">{Math.round(ratioData.water * (task.requiredVolume - task.completedVolume))} kg</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">砂：</span>
                    <span className="font-medium">{Math.round(ratioData.sand * (task.requiredVolume - task.completedVolume))} kg</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">石子：</span>
                    <span className="font-medium">{Math.round(ratioData.gravel * (task.requiredVolume - task.completedVolume))} kg</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
