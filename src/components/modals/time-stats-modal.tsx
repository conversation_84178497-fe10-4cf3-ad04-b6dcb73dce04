'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Timer, Clock, TrendingUp, BarChart3, 
  Pause, Play, AlertCircle, CheckCircle 
} from 'lucide-react';
import type { Task } from '@/types';

interface TimeStatsModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
}

// 时间统计数据接口
interface TimeStats {
  totalTime: number;        // 总用时（分钟）
  productionTime: number;   // 生产时间（分钟）
  waitingTime: number;      // 等待时间（分钟）
  pausedTime: number;       // 暂停时间（分钟）
  averageDispatchInterval: number; // 平均发车间隔（分钟）
  totalDispatches: number;  // 总发车次数
  efficiency: number;       // 效率百分比
}

export function TimeStatsModal({
  isOpen,
  onOpenChange,
  task,
}: TimeStatsModalProps) {
  // 模拟时间统计数据
  const getTimeStats = (task: Task): TimeStats => {
    const totalDispatches = Math.floor(task.completedVolume / 8); // 假设每车8方
    const totalTime = totalDispatches * 45; // 假设每车平均45分钟
    const productionTime = totalDispatches * 30; // 生产时间30分钟
    const waitingTime = totalDispatches * 10; // 等待时间10分钟
    const pausedTime = totalDispatches * 5; // 暂停时间5分钟
    
    return {
      totalTime,
      productionTime,
      waitingTime,
      pausedTime,
      averageDispatchInterval: task.dispatchFrequencyMinutes || 30,
      totalDispatches,
      efficiency: Math.round((productionTime / totalTime) * 100),
    };
  };

  if (!task) return null;

  const stats = getTimeStats(task);

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 80) return 'text-green-600';
    if (efficiency >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getEfficiencyBadge = (efficiency: number) => {
    if (efficiency >= 80) return <Badge className="bg-green-100 text-green-800">优秀</Badge>;
    if (efficiency >= 60) return <Badge className="bg-yellow-100 text-yellow-800">良好</Badge>;
    return <Badge className="bg-red-100 text-red-800">需改进</Badge>;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Timer className="h-5 w-5" />
            自由时间统计 - {task.taskNumber}
          </DialogTitle>
          <DialogDescription>
            查看任务 {task.projectName} 的详细时间统计和效率分析
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 总体统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">总用时</p>
                    <p className="text-lg font-bold">{formatTime(stats.totalTime)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Play className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">生产时间</p>
                    <p className="text-lg font-bold text-green-600">{formatTime(stats.productionTime)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-orange-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">等待时间</p>
                    <p className="text-lg font-bold text-orange-600">{formatTime(stats.waitingTime)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Pause className="h-4 w-4 text-red-600" />
                  <div>
                    <p className="text-xs text-muted-foreground">暂停时间</p>
                    <p className="text-lg font-bold text-red-600">{formatTime(stats.pausedTime)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 效率分析 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                效率分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">生产效率</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-lg font-bold ${getEfficiencyColor(stats.efficiency)}`}>
                        {stats.efficiency}%
                      </span>
                      {getEfficiencyBadge(stats.efficiency)}
                    </div>
                  </div>
                  
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all ${
                        stats.efficiency >= 80 ? 'bg-green-600' :
                        stats.efficiency >= 60 ? 'bg-yellow-600' : 'bg-red-600'
                      }`}
                      style={{ width: `${stats.efficiency}%` }}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">平均发车间隔</span>
                    <span className="font-medium">{stats.averageDispatchInterval} 分钟</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">总发车次数</span>
                    <span className="font-medium">{stats.totalDispatches} 次</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">平均每车用时</span>
                    <span className="font-medium">{formatTime(Math.round(stats.totalTime / stats.totalDispatches))}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 时间分布 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                时间分布
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span className="text-sm flex-1">生产时间</span>
                  <span className="text-sm font-medium">{formatTime(stats.productionTime)}</span>
                  <span className="text-xs text-muted-foreground">
                    ({Math.round((stats.productionTime / stats.totalTime) * 100)}%)
                  </span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-orange-500 rounded"></div>
                  <span className="text-sm flex-1">等待时间</span>
                  <span className="text-sm font-medium">{formatTime(stats.waitingTime)}</span>
                  <span className="text-xs text-muted-foreground">
                    ({Math.round((stats.waitingTime / stats.totalTime) * 100)}%)
                  </span>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span className="text-sm flex-1">暂停时间</span>
                  <span className="text-sm font-medium">{formatTime(stats.pausedTime)}</span>
                  <span className="text-xs text-muted-foreground">
                    ({Math.round((stats.pausedTime / stats.totalTime) * 100)}%)
                  </span>
                </div>
              </div>

              {/* 可视化时间分布条 */}
              <div className="mt-4 w-full h-6 bg-muted rounded-full overflow-hidden flex">
                <div 
                  className="bg-green-500 h-full"
                  style={{ width: `${(stats.productionTime / stats.totalTime) * 100}%` }}
                />
                <div 
                  className="bg-orange-500 h-full"
                  style={{ width: `${(stats.waitingTime / stats.totalTime) * 100}%` }}
                />
                <div 
                  className="bg-red-500 h-full"
                  style={{ width: `${(stats.pausedTime / stats.totalTime) * 100}%` }}
                />
              </div>
            </CardContent>
          </Card>

          {/* 改进建议 */}
          {stats.efficiency < 80 && (
            <Card className="bg-blue-50">
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  改进建议
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="text-sm space-y-1 text-blue-800">
                  {stats.waitingTime > stats.productionTime * 0.3 && (
                    <li>• 等待时间较长，建议优化车辆调度安排</li>
                  )}
                  {stats.pausedTime > stats.totalTime * 0.1 && (
                    <li>• 暂停时间偏多，建议检查生产线设备状态</li>
                  )}
                  {stats.averageDispatchInterval > 45 && (
                    <li>• 发车间隔较长，可考虑缩短间隔提高效率</li>
                  )}
                  <li>• 建议设定合理的发车提醒，减少等待时间</li>
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
