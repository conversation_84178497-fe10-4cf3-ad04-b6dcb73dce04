'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Building, MapPin, Users } from 'lucide-react';
import type { Task } from '@/types';

interface TaskAbbreviationModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
  onSave: (taskId: string, abbreviation: string) => void;
}

export function TaskAbbreviationModal({
  isOpen,
  onOpenChange,
  task,
  onSave,
}: TaskAbbreviationModalProps) {
  const [abbreviation, setAbbreviation] = useState('');

  // 初始化缩写
  useEffect(() => {
    if (task && isOpen) {
      setAbbreviation(task.projectAbbreviation || '');
    }
  }, [task, isOpen]);

  const handleSave = () => {
    if (!task) return;
    onSave(task.id, abbreviation);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  // 自动生成缩写建议
  const generateAbbreviation = () => {
    if (!task) return;
    
    const projectName = task.projectName || '';
    const constructionSite = task.constructionSite || '';
    
    // 简单的缩写生成逻辑：取项目名称和施工部位的首字符
    const projectChars = projectName.slice(0, 2);
    const siteChars = constructionSite.slice(0, 2);
    const suggested = `${projectChars}${siteChars}`.toUpperCase();
    
    setAbbreviation(suggested);
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            任务缩写设置 - {task.taskNumber}
          </DialogTitle>
          <DialogDescription>
            设置任务的显示缩写，用于在界面中简化显示。
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 任务缩写输入 */}
          <div className="grid gap-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="abbreviation">任务缩写</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={generateAbbreviation}
                className="text-xs"
              >
                自动生成
              </Button>
            </div>
            <Input
              id="abbreviation"
              value={abbreviation}
              onChange={(e) => setAbbreviation(e.target.value)}
              placeholder="请输入任务缩写"
              maxLength={10}
            />
            <p className="text-xs text-muted-foreground">
              建议使用2-6个字符，便于在界面中快速识别
            </p>
          </div>

          {/* 任务信息展示（只读） */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">任务详细信息（只读）</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <Users className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <Label className="text-xs text-muted-foreground">客户名称</Label>
                  <p className="text-sm font-medium truncate">
                    {task.customerName || task.constructionUnit || '未设置'}
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Building className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <Label className="text-xs text-muted-foreground">具体工程名称</Label>
                  <p className="text-sm font-medium">
                    {task.projectName || '未设置'}
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <Label className="text-xs text-muted-foreground">施工部位</Label>
                  <p className="text-sm font-medium">
                    {task.constructionSite || task.constructionLocation || '未设置'}
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Building className="h-4 w-4 mt-0.5 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <Label className="text-xs text-muted-foreground">施工单位</Label>
                  <p className="text-sm font-medium">
                    {task.constructionCompany || task.constructionUnit || '未设置'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 预览效果 */}
          {abbreviation && (
            <Card className="bg-muted/50">
              <CardContent className="pt-4">
                <Label className="text-xs text-muted-foreground">预览效果</Label>
                <div className="mt-1 p-2 bg-background rounded border text-sm font-medium">
                  {abbreviation}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={!abbreviation.trim()}>
            保存缩写
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
