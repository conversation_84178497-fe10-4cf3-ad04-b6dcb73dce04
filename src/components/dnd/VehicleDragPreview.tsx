// src/components/dnd/VehicleDragPreview.tsx
'use client';

import React from 'react';
import type { Vehicle, VehicleDisplayMode } from '@/types';
import { cn } from '@/lib/utils';
import { TruckIcon } from 'lucide-react';
import { useUiStore } from '@/store/uiStore'; // Import useUiStore

interface VehicleDragPreviewProps {
  vehicle: Vehicle;
  // displayMode: VehicleDisplayMode; // displayMode will be fetched from uiStore
  style?: React.CSSProperties; // For positioning by CustomDragLayer
}

export const VehicleDragPreview: React.FC<VehicleDragPreviewProps> = ({ vehicle, style }) => {
  const vehicleDisplayMode = useUiStore(state => state.vehicleDisplayMode); // Get displayMode from store
  const effectiveOperationalStatus = vehicle.operationalStatus || 'normal';

  const getStatusColors = () => {
    // Simplified colors for preview, can be expanded
    if (effectiveOperationalStatus === 'deactivated' || effectiveOperationalStatus === 'paused') {
      return 'border-gray-400 bg-gray-200 text-gray-600';
    }
    switch (vehicle.status) {
      case 'pending': return 'border-primary bg-primary/20 text-primary-foreground';
      case 'returned': return 'border-destructive bg-destructive/20 text-destructive-foreground';
      default: return 'border-gray-300 bg-gray-100 text-gray-800';
    }
  };

  const statusColorClasses = getStatusColors();
  const displayText = vehicleDisplayMode === 'licensePlate' ? vehicle.vehicleNumber : vehicle.id;

  return (
    <div
      style={style} // Applied by CustomDragLayer for positioning
      className={cn(
        "p-1 border rounded-md shadow-xl flex items-center justify-center text-[10px] gap-1 h-8 w-auto min-w-[60px] max-w-[120px]", // Fixed size or slightly dynamic
        statusColorClasses,
        "transition-none" // Important: remove transitions for drag preview
      )}
    >
      {vehicleDisplayMode === 'internalId' && <TruckIcon className="h-2.5 w-2.5 text-muted-foreground mr-0.5 flex-shrink-0" />}
      <div className={cn("flex-1 flex items-center justify-center overflow-hidden min-w-0 px-0.5")}>
        <span className="font-semibold truncate" style={{ minWidth: '10px' }}>{displayText}</span>
      </div>
    </div>
  );
};
