
import React from 'react';
import type { Vehicle } from '@/types';
import { VehicleDispatch } from '@/components/sections/vehicle-dispatch';

const MemoizedVehicleDispatch = React.memo(VehicleDispatch);

interface RightPanelProps {
  // onReorderVehicles prop is removed
}

export function RightPanel({
  // onReorderVehicles removed from props
}: RightPanelProps) {
  return (
    <div className="h-full p-0.5 pl-0">
      <MemoizedVehicleDispatch
        // onReorderVehicles prop is no longer passed
      />
    </div>
  );
}
