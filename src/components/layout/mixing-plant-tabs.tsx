// src/components/layout/mixing-plant-tabs.tsx
'use client';
import React, { useCallback } from 'react';
import { useDrop, DropTargetMonitor } from 'react-dnd';
import type { Plant, Vehicle } from '@/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RingProgress } from '@/components/shared/ring-progress';
import { cn } from '@/lib/utils';
import { ItemTypes } from '@/constants/dndItemTypes'; // Import item types

interface DraggableVehicleItem {
  vehicle: Vehicle;
  index: number;
  statusList: 'pending' | 'returned';
  type: typeof ItemTypes.VEHICLE_CARD_DISPATCH;
}

interface PlantTabTriggerProps {
  plant: Plant;
  isSelected: boolean;
  onSelectPlant: (plantId: string) => void;
  onInitiateCrossPlantDispatch: (vehicleId: string, targetPlantId: string) => void;
}

const PlantTabTrigger: React.FC<PlantTabTriggerProps> = ({
  plant,
  isSelected,
  onSelectPlant,
  onInitiateCrossPlantDispatch,
}) => {
  const [{ canDrop, isOver }, drop] = useDrop({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    canDrop: (item: DraggableVehicleItem, monitor) => {
      return item.vehicle.plantId !== plant.id && !isSelected;
    },
    drop: (item: DraggableVehicleItem, monitor) => {
      if (monitor.canDrop()) {
        onInitiateCrossPlantDispatch(item.vehicle.id, plant.id);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const isActiveDropTarget = isOver && canDrop;

  return (
    <TabsTrigger
      ref={drop}
      key={plant.id}
      value={plant.id}
      onClick={() => onSelectPlant(plant.id)}
      className={cn(
        "px-4 pt-2 pb-[calc(0.5rem+2px)] text-xs transition-all rounded-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none relative",
        "border-b-2",
        "data-[state=inactive]:text-muted-foreground data-[state=inactive]:border-transparent",
        "data-[state=inactive]:hover:text-primary data-[state=inactive]:hover:border-primary/30",
        "data-[state=active]:text-primary data-[state=active]:font-semibold data-[state=active]:border-primary",
        isActiveDropTarget && "bg-accent/20 border-accent ring-1 ring-accent rounded-t-md shadow-inner scale-105",
        !canDrop && isOver && !isSelected && "opacity-50 cursor-not-allowed" // Visual cue for invalid drop (e.g., same plant)
      )}
      style={{ marginBottom: '-1px' }}
      // Remove dnd-kit specific event handlers
    >
      <div className="flex items-center space-x-1.5 pointer-events-none"> {/* Make content non-interactive for drop */}
        <RingProgress
          value={(plant.stats.totalTasks > 0 ? (plant.stats.completedTasks / plant.stats.totalTasks) : 0) * 100}
          radius={7}
          strokeWidth={2}
          label={plant.stats.totalTasks > 0 ? String(Math.round((plant.stats.completedTasks / plant.stats.totalTasks) * 100) + '%') : '0%'}
        />
        <span>{plant.name}</span>
      </div>
    </TabsTrigger>
  );
};

interface MixingPlantTabsProps {
  plants: Plant[];
  selectedPlantId: string | null;
  onSelectPlant: (plantId: string) => void;
  onInitiateCrossPlantDispatch: (vehicleId: string, targetPlantId: string) => void;
}

export function MixingPlantTabs({
  plants,
  selectedPlantId,
  onSelectPlant,
  onInitiateCrossPlantDispatch
}: MixingPlantTabsProps) {
  return (
    <Tabs value={selectedPlantId || undefined} onValueChange={onSelectPlant} className="w-auto">
      <TabsList className="bg-transparent p-0 h-auto border-b flex space-x-1">
        {plants.map((plant) => (
          <PlantTabTrigger
            key={plant.id}
            plant={plant}
            isSelected={selectedPlantId === plant.id}
            onSelectPlant={onSelectPlant}
            onInitiateCrossPlantDispatch={onInitiateCrossPlantDispatch}
          />
        ))}
      </TabsList>
    </Tabs>
  );
}
