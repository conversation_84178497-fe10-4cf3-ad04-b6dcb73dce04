'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Settings } from 'lucide-react';
import { useReminderStore, generateMockReminderConfigs, addMockReminderMessages } from '@/store/reminderStore';
import { useAppStore } from '@/store/appStore';
import { generateMockTasks } from '@/data/mock-data';
import { DataSourceSwitcher } from '@/utils/dataSourceSwitcher';

export function DevTools() {
  const [count, setCount] = useState<number>(500);
  const { clearMessages } = useReminderStore();
  const tasks = useAppStore(state => state.tasks);
  const setTasks = useAppStore(state => state.setMockTasks);
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSourceInfo, setDataSourceInfo] = useState(DataSourceSwitcher.getConfigInfo());

  // 根据当前任务生成提醒配置
  const generateConfigsForCurrentTasks = () => {
    const reminderStore = useReminderStore.getState();
    
    // 清除现有配置
    const oldConfigs = reminderStore.configs;
    reminderStore.importConfigs([]);
    
    // 为每个InProgress状态的任务创建提醒配置
    const inProgressTasks = tasks.filter(t => t.dispatchStatus === 'InProgress');
    let count = 0;
    
    inProgressTasks.forEach(task => {
      reminderStore.addConfig({
        taskId: task.id,
        enabled: true,
        reminderTypes: ['dispatchCountdown', 'highlight', 'popup', 'sound'],
        reminderFrequencyMinutes: 5,
        reminderLevels: [
          { minutes: 30, types: ['dispatchCountdown'] },
          { minutes: 15, types: ['dispatchCountdown', 'highlight'] },
          { minutes: 5, types: ['dispatchCountdown', 'highlight', 'popup', 'sound'] }
        ],
        defaultDispatchFrequencyMinutes: 30
      });
      count++;
    });
    
    alert(`已为${count}个进行中任务生成提醒配置（共${inProgressTasks.length}个任务）`);
  };

  // 生成新的带倒计时的任务
  const generateCountdownTasks = () => {
    try {
      setLoading(true);
      const newTasks = generateMockTasks();
      
      // 设置模拟任务
      setTasks(newTasks);
      
      // 为这些任务生成配置
      setTimeout(() => {
        generateMockReminderConfigs(newTasks.length);
        setLoading(false);
        alert(`已生成${newTasks.length}个带倒计时的任务并为其创建提醒配置`);
      }, 500);
    } catch (error) {
      setLoading(false);
      alert(`生成任务时出错: ${error}`);
    }
  };
  
  // 加载全部测试场景
  const loadAllTestScenarios = async () => {
    try {
      setLoading(true);
      
      // 第一步：清空现有数据
      clearMessages();
      useReminderStore.getState().importConfigs([]);
      
      // 第二步：生成所有场景的任务
      const newTasks = generateMockTasks();
      setTasks(newTasks);
      
      // 第三步：根据任务场景生成匹配的提醒配置
      setTimeout(() => {
        generateMockReminderConfigs(newTasks.length);
        
        // 第四步：添加一些示例提醒消息
        setTimeout(() => {
          addMockReminderMessages();
          setLoading(false);
          alert(`已加载全部测试场景：
- ${newTasks.length}个不同场景的任务
- 针对各场景的专用提醒配置
- 示例提醒消息
请查看任务列表查看效果`);
        }, 300);
      }, 500);
    } catch (error) {
      setLoading(false);
      alert(`加载测试场景时出错: ${error}`);
    }
  };

  // 仅在开发环境下显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="icon" className="rounded-full bg-background/80 backdrop-blur-sm">
            <Settings className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <div className="space-y-4">
            <h4 className="font-medium text-sm">开发工具</h4>
            
            <div className="space-y-2">
              <div className="text-xs font-medium">提醒配置生成</div>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    min="1"
                    max="10000"
                    value={count}
                    onChange={(e) => setCount(parseInt(e.target.value) || 100)}
                    className="h-8"
                  />
                  <Button 
                    variant="default" 
                    size="sm"
                    onClick={() => {
                      generateMockReminderConfigs(count);
                      alert(`已生成 ${count} 条模拟提醒配置`);
                    }}
                  >
                    生成配置
                  </Button>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={generateConfigsForCurrentTasks}
                >
                  为当前任务生成配置
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-xs font-medium">提醒消息</div>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    addMockReminderMessages();
                    alert('已添加模拟提醒消息');
                  }}
                >
                  添加消息
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm"
                  onClick={() => {
                    clearMessages();
                    alert('已清除所有提醒消息');
                  }}
                >
                  清除消息
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-xs font-medium">生成带倒计时任务</div>
              <Button 
                variant="default" 
                size="sm"
                className="w-full"
                onClick={generateCountdownTasks}
                disabled={loading}
              >
                {loading ? '生成中...' : '生成带倒计时的任务'}
              </Button>
              <p className="text-[10px] text-muted-foreground">
                生成带有各种倒计时状态的任务数据，并自动创建相应的提醒配置
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="text-xs font-medium text-accent">一键加载全部测试场景</div>
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={loadAllTestScenarios}
                disabled={loading}
              >
                {loading ? '加载中...' : '加载全部测试场景'}
              </Button>
              <p className="text-[10px] text-muted-foreground">
                一键加载全部测试场景，包括14种任务场景、对应提醒配置和示例消息
              </p>
            </div>

            <div className="space-y-2 border-t pt-2">
              <div className="text-xs font-medium">数据源切换</div>
              <div className="flex items-center justify-between">
                <div className="text-xs text-muted-foreground">
                  当前: {dataSourceInfo.mode === 'mock' ? 'Mock数据' : 'API数据'}
                </div>
                <Button
                  variant={dataSourceInfo.mode === 'mock' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    const newMode = DataSourceSwitcher.toggleMode();
                    setDataSourceInfo(DataSourceSwitcher.getConfigInfo());
                    alert(`已切换到${newMode === 'mock' ? 'Mock数据' : 'API数据'}模式，页面将刷新`);
                    setTimeout(() => window.location.reload(), 1000);
                  }}
                >
                  切换到{dataSourceInfo.mode === 'mock' ? 'API' : 'Mock'}
                </Button>
              </div>
              <p className="text-[10px] text-muted-foreground">
                {dataSourceInfo.description}
              </p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
} 