'use client';

import React, { useState, useEffect } from 'react';
import { BellRing, X, Check, Calendar, Clock } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { IconButton } from '@/components/shared/icon-button';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useReminderStore, addMockReminderMessages } from '@/store/reminderStore';
import type { ReminderMessage } from '@/types';

interface ReminderMessageItemProps {
  message: ReminderMessage;
  onMarkAsRead: (id: string) => void;
}

const ReminderMessageItem: React.FC<ReminderMessageItemProps> = ({ message, onMarkAsRead }) => {
  // 格式化时间为"今天 HH:mm"或"MM-DD HH:mm"
  const formatMessageTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.getDate() === now.getDate() && 
                    date.getMonth() === now.getMonth() && 
                    date.getFullYear() === now.getFullYear();
    
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    if (isToday) {
      return `今天 ${hours}:${minutes}`;
    } else {
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${month}-${day} ${hours}:${minutes}`;
    }
  };

  return (
    <div className={cn(
      "px-4 py-3 border-b last:border-0",
      !message.read && "bg-primary/5"
    )}>
      <div className="flex justify-between items-start">
        <div className="flex items-start gap-2">
          <div className="mt-0.5">
            <BellRing className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">{message.title}</span>
              {!message.read && (
                <Badge variant="secondary" className="px-1.5 h-5 bg-primary/20 text-primary text-[10px]">
                  新消息
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground text-xs mt-1">{message.description}</p>
            <div className="flex items-center gap-2 mt-2 text-[10px] text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                <span>{message.taskNumber}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                <span>{formatMessageTime(message.time)}</span>
              </div>
            </div>
          </div>
        </div>
        {!message.read && (
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6 text-primary" 
            onClick={() => onMarkAsRead(message.id)}
          >
            <Check className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

export function ReminderMessageList() {
  const [open, setOpen] = useState(false);
  const { messages, markAsRead, markAllAsRead } = useReminderStore();
  
  // 在开发环境下，如果没有消息，添加一些测试消息
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && messages.length === 0) {
      addMockReminderMessages();
    }
  }, [messages.length]);
  
  // 未读消息数量
  const unreadCount = messages.filter(msg => !msg.read).length;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div className="relative">
          <IconButton icon={BellRing} tooltipLabel="提醒消息" />
          {unreadCount > 0 && (
            <Badge 
              className="absolute -top-1 -right-1 px-1 min-w-[16px] h-4 text-[10px] flex items-center justify-center bg-destructive text-destructive-foreground"
              variant="destructive"
            >
              {unreadCount}
            </Badge>
          )}
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
          <DialogTitle>提醒消息</DialogTitle>
          {unreadCount > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 text-xs" 
              onClick={markAllAsRead}
            >
              全部标为已读
            </Button>
          )}
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] -mx-6">
          {messages.length > 0 ? (
            messages.map(message => (
              <ReminderMessageItem 
                key={message.id} 
                message={message} 
                onMarkAsRead={markAsRead} 
              />
            ))
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              暂无提醒消息
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
} 