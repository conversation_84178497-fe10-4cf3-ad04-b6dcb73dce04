'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import type { MaterialInfo, MixingStation } from '@/types/mixRatio';

interface MaterialInfoPanelProps {
  materialInfos: MaterialInfo[];
  selectedStation: string;
  stations: MixingStation[];
}

export function MaterialInfoPanel({ 
  materialInfos, 
  selectedStation, 
  stations 
}: MaterialInfoPanelProps) {
  
  // 按搅拌站分组物料信息
  const groupedMaterials = materialInfos.reduce((acc, material) => {
    const stationName = material.stationName;
    if (!acc[stationName]) {
      acc[stationName] = [];
    }
    acc[stationName].push(material);
    return acc;
  }, {} as Record<string, MaterialInfo[]>);

  // 合并相同物料的不同规格
  const getMergedMaterials = (materials: MaterialInfo[]) => {
    const merged: Array<{
      material: string;
      specifications: string[];
      siloName: string;
      rowSpan: number;
      isFirst: boolean;
    }> = [];

    const materialGroups = materials.reduce((acc, material) => {
      if (!acc[material.material]) {
        acc[material.material] = {
          specifications: [],
          siloName: material.siloName
        };
      }
      acc[material.material].specifications.push(...material.specifications);
      return acc;
    }, {} as Record<string, { specifications: string[]; siloName: string }>);

    Object.entries(materialGroups).forEach(([material, data]) => {
      const uniqueSpecs = [...new Set(data.specifications)];
      uniqueSpecs.forEach((spec, index) => {
        merged.push({
          material,
          specifications: [spec],
          siloName: data.siloName,
          rowSpan: index === 0 ? uniqueSpecs.length : 0,
          isFirst: index === 0
        });
      });
    });

    return merged;
  };

  return (
    <div className="w-80 border-l bg-muted/20">
      <div className="p-4 border-b">
        <h3 className="text-lg font-semibold">物料信息</h3>
      </div>
      
      <div className="p-4 space-y-4 overflow-auto">
        {Object.entries(groupedMaterials).map(([stationName, materials]) => (
          <Card key={stationName}>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Badge variant={materials[0]?.stationId === selectedStation ? 'default' : 'secondary'}>
                  {stationName}
                </Badge>
              </CardTitle>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="text-xs font-medium">物料</TableHead>
                      <TableHead className="text-xs font-medium">规格</TableHead>
                      <TableHead className="text-xs font-medium">仓名</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getMergedMaterials(materials).map((item, index) => (
                      <TableRow key={`${item.material}-${index}`} className="text-xs">
                        {/* 物料名称 - 合并单元格 */}
                        {item.isFirst && (
                          <TableCell 
                            rowSpan={item.rowSpan} 
                            className="font-medium align-top bg-muted/30"
                          >
                            {item.material}
                          </TableCell>
                        )}
                        
                        {/* 规格 */}
                        <TableCell className="text-muted-foreground">
                          {item.specifications[0]}
                        </TableCell>
                        
                        {/* 仓名 */}
                        <TableCell className="text-muted-foreground">
                          {item.siloName}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* 当前选中搅拌站的详细信息 */}
        {selectedStation && (
          <Card className="border-primary/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-base text-primary">
                当前搅拌站详情
              </CardTitle>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">搅拌站ID:</span>
                  <span className="font-medium">{selectedStation}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">搅拌站名称:</span>
                  <span className="font-medium">
                    {stations.find(s => s.id === selectedStation)?.name || '未知'}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">物料种类:</span>
                  <span className="font-medium">
                    {Object.keys(groupedMaterials).length} 种
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">状态:</span>
                  <Badge variant="default" className="text-xs">
                    运行中
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 物料统计信息 */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">物料统计</CardTitle>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">总物料种类:</span>
                <span className="font-medium">
                  {new Set(materialInfos.map(m => m.material)).size} 种
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-muted-foreground">总规格数量:</span>
                <span className="font-medium">
                  {materialInfos.reduce((sum, m) => sum + m.specifications.length, 0)} 个
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-muted-foreground">活跃搅拌站:</span>
                <span className="font-medium">
                  {stations.filter(s => s.isActive).length} 个
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 快速操作提示 */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-3">
            <div className="text-xs text-blue-800">
              <div className="font-medium mb-1">操作提示:</div>
              <ul className="space-y-1 list-disc list-inside">
                <li>点击物料可快速添加到配比表格</li>
                <li>不同搅拌站的物料配置独立管理</li>
                <li>规格信息可在材料管理中修改</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
