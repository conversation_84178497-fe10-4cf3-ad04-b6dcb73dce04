'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Database, Edit, Save, X, CheckCircle, XCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

import type { SiloInfo, MixingStation, MaterialType } from '@/types/mixRatio';

interface SiloManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedStation: string;
  stations: MixingStation[];
  silos: SiloInfo[];
  onSilosChange: (silos: SiloInfo[]) => void;
}

export function SiloManagementModal({
  isOpen,
  onClose,
  selectedStation,
  stations,
  silos,
  onSilosChange
}: SiloManagementModalProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingSilo, setEditingSilo] = useState<Partial<SiloInfo>>({});
  const [currentStation, setCurrentStation] = useState(selectedStation);
  const { toast } = useToast();

  const materialTypes: { value: MaterialType; label: string }[] = [
    { value: 'cement', label: '水泥' },
    { value: 'water', label: '水' },
    { value: 'sand', label: '砂' },
    { value: 'stone', label: '石子' },
    { value: 'additive', label: '外加剂' },
    { value: 'admixture', label: '掺合料' },
    { value: 'flyash', label: '粉煤灰' },
    { value: 'slag', label: '矿粉' },
  ];

  const filteredSilos = silos.filter(s => s.stationId === currentStation);

  const handleEdit = (silo: SiloInfo) => {
    setEditingId(silo.id);
    setEditingSilo({ ...silo });
  };

  const handleSave = () => {
    if (!editingId || !editingSilo.name?.trim()) {
      toast({
        title: '保存失败',
        description: '请填写完整的料仓信息',
        variant: 'destructive'
      });
      return;
    }

    const updatedSilos = silos.map(s => 
      s.id === editingId ? { ...s, ...editingSilo } as SiloInfo : s
    );
    
    onSilosChange(updatedSilos);
    setEditingId(null);
    setEditingSilo({});
    
    toast({
      title: '保存成功',
      description: '料仓信息已更新'
    });
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingSilo({});
  };

  const handleToggleEnabled = (id: string, enabled: boolean) => {
    const updatedSilos = silos.map(s => 
      s.id === id ? { ...s, isEnabled: enabled } : s
    );
    onSilosChange(updatedSilos);
    
    toast({
      title: enabled ? '料仓已启用' : '料仓已禁用',
      description: `料仓 ${silos.find(s => s.id === id)?.name} ${enabled ? '启用' : '禁用'}成功`
    });
  };

  const getStatusBadge = (isEnabled: boolean) => {
    return isEnabled ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <CheckCircle className="mr-1 h-3 w-3" />
        启用
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">
        <XCircle className="mr-1 h-3 w-3" />
        禁用
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            存放地名称管理（料仓管理）
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搅拌站选择 */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium">搅拌站:</span>
                <Select value={currentStation} onValueChange={setCurrentStation}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="选择搅拌站" />
                  </SelectTrigger>
                  <SelectContent>
                    {stations.map((station) => (
                      <SelectItem key={station.id} value={station.id}>
                        {station.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Badge variant="outline">
                  料仓数量: {filteredSilos.length}
                </Badge>
                
                <Badge variant="outline" className="bg-green-50">
                  启用: {filteredSilos.filter(s => s.isEnabled).length}
                </Badge>
                
                <Badge variant="outline" className="bg-red-50">
                  禁用: {filteredSilos.filter(s => !s.isEnabled).length}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* 说明信息 */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="text-sm text-blue-800">
                <div className="font-medium mb-2">料仓管理说明：</div>
                <ul className="space-y-1 list-disc list-inside text-xs">
                  <li><strong>罐标识</strong>：系统预设，不可修改（如：粉料1、骨料1、水1、外加剂1）</li>
                  <li><strong>罐名称</strong>：可自定义，方便过磅员选择卸料地（如：站1#水泥、站2#机制砂）</li>
                  <li><strong>显示次序</strong>：控制料仓在界面中的显示顺序</li>
                  <li><strong>启用状态</strong>：只有启用的料仓才会在配比中生效，请务必勾选"启用"</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* 料仓表格 */}
          <div className="border rounded-lg overflow-hidden">
            <div className="max-h-96 overflow-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow>
                    <TableHead className="w-24">罐标识</TableHead>
                    <TableHead className="w-32">罐名称</TableHead>
                    <TableHead className="w-20">显示次序</TableHead>
                    <TableHead className="w-24">材料类型</TableHead>
                    <TableHead className="w-20">启用状态</TableHead>
                    <TableHead className="w-20">启用</TableHead>
                    <TableHead className="w-24">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSilos.map((silo) => (
                    <TableRow key={silo.id} className={!silo.isEnabled ? 'opacity-60' : ''}>
                      {/* 罐标识 - 不可修改 */}
                      <TableCell>
                        <Badge variant="outline" className="font-mono">
                          {silo.identifier}
                        </Badge>
                      </TableCell>
                      
                      {/* 罐名称 - 可修改 */}
                      <TableCell>
                        {editingId === silo.id ? (
                          <Input
                            value={editingSilo.name || ''}
                            onChange={(e) => setEditingSilo({
                              ...editingSilo,
                              name: e.target.value
                            })}
                            className="h-8"
                            placeholder="罐名称"
                          />
                        ) : (
                          <span className="font-medium">{silo.name}</span>
                        )}
                      </TableCell>
                      
                      {/* 显示次序 - 可修改 */}
                      <TableCell>
                        {editingId === silo.id ? (
                          <Input
                            type="number"
                            value={editingSilo.displayOrder || ''}
                            onChange={(e) => setEditingSilo({
                              ...editingSilo,
                              displayOrder: Number(e.target.value)
                            })}
                            className="h-8 w-16"
                            min="1"
                          />
                        ) : (
                          <span className="text-center block">{silo.displayOrder}</span>
                        )}
                      </TableCell>
                      
                      {/* 材料类型 */}
                      <TableCell>
                        {editingId === silo.id ? (
                          <Select
                            value={editingSilo.materialType || silo.materialType}
                            onValueChange={(value: MaterialType) => setEditingSilo({
                              ...editingSilo,
                              materialType: value
                            })}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {materialTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <Badge variant="secondary">
                            {materialTypes.find(t => t.value === silo.materialType)?.label || silo.materialType}
                          </Badge>
                        )}
                      </TableCell>
                      
                      {/* 启用状态显示 */}
                      <TableCell>
                        {getStatusBadge(silo.isEnabled)}
                      </TableCell>
                      
                      {/* 启用复选框 */}
                      <TableCell>
                        <Checkbox
                          checked={silo.isEnabled}
                          onCheckedChange={(checked) => handleToggleEnabled(silo.id, checked as boolean)}
                        />
                      </TableCell>
                      
                      {/* 操作按钮 */}
                      <TableCell>
                        {editingId === silo.id ? (
                          <div className="flex space-x-1">
                            <Button size="sm" onClick={handleSave}>
                              <Save className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={handleCancel}>
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <Button size="sm" variant="outline" onClick={() => handleEdit(silo)}>
                            <Edit className="h-3 w-3" />
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-4 gap-4">
            {materialTypes.slice(0, 4).map((type) => {
              const count = filteredSilos.filter(s => s.materialType === type.value).length;
              const enabledCount = filteredSilos.filter(s => s.materialType === type.value && s.isEnabled).length;
              return (
                <Card key={type.value}>
                  <CardContent className="p-3">
                    <div className="text-center">
                      <div className="text-lg font-bold text-primary">{enabledCount}/{count}</div>
                      <div className="text-xs text-muted-foreground">{type.label}料仓</div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
