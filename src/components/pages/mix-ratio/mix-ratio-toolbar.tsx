'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  History, 
  Settings, 
  RefreshCw, 
  Printer,
  Database,
  Users,
  Calculator
} from 'lucide-react';

import type { MixingStation } from '@/types/mixRatio';

interface MixRatioToolbarProps {
  unifiedRatio: boolean;
  onUnifiedRatioChange: (checked: boolean) => void;
  selectedStation: string;
  onStationChange: (stationId: string) => void;
  stations: MixingStation[];
  selectedRatioCode: string;
  onRatioCodeChange: (code: string) => void;
  onOpenHistory: () => void;
  onOpenMaterialManagement: () => void;
  onOpenSiloManagement: () => void;
  onOpenQualityStandard: () => void;
  onOpenParameterSettings: () => void;
  onRefreshSilos: () => void;
}

export function MixRatioToolbar({
  unifiedRatio,
  onUnifiedRatioChange,
  selectedStation,
  onStationChange,
  stations,
  selectedRatioCode,
  onRatioCodeChange,
  onOpenHistory,
  onOpenMaterialManagement,
  onOpenSiloManagement,
  onOpenQualityStandard,
  onOpenParameterSettings,
  onRefreshSilos,
}: MixRatioToolbarProps) {
  
  // 模拟配比编号列表
  const ratioCodes = [
    'C125-00003',
    'C125-00004', 
    'C125-00005',
    'C130-00001',
    'C130-00002'
  ];

  return (
    <TooltipProvider>
      <div className="border-b bg-muted/30 p-2">
        <div className="flex items-center justify-between">
          {/* 左侧控件 */}
          <div className="flex items-center space-x-3">
          {/* 所有站统一配比 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="unified-ratio"
              checked={unifiedRatio}
              onCheckedChange={onUnifiedRatioChange}
            />
            <Label htmlFor="unified-ratio" className="text-xs font-medium">
              统一配比
            </Label>
          </div>

          {/* 搅拌站选择 */}
          <div className="flex items-center space-x-1">
            <Label className="text-xs">站点:</Label>
            <Select value={selectedStation} onValueChange={onStationChange}>
              <SelectTrigger className="w-24 h-7">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {stations.map((station) => (
                  <SelectItem key={station.id} value={station.id}>
                    {station.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 配比编号选择 */}
          <div className="flex items-center space-x-1">
            <Label className="text-xs">配比:</Label>
            <Select value={selectedRatioCode} onValueChange={onRatioCodeChange}>
              <SelectTrigger className="w-28 h-7">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ratioCodes.map((code) => (
                  <SelectItem key={code} value={code}>
                    {code}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 右侧控件 */}
        <div className="flex items-center space-x-2">
          {/* 料仓管理 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={onOpenSiloManagement}>
                <Database className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>料仓管理</TooltipContent>
          </Tooltip>

          {/* 材料管理 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={onOpenMaterialManagement}>
                <Users className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>材料管理</TooltipContent>
          </Tooltip>

          {/* 历史记录 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={onOpenHistory}>
                <History className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>历史记录</TooltipContent>
          </Tooltip>

          {/* 设置 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={onOpenParameterSettings}>
                <Settings className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>参数设置</TooltipContent>
          </Tooltip>

          {/* 打印 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm">
                <Printer className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>打印配比</TooltipContent>
          </Tooltip>

          {/* 刷新 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={onRefreshSilos}>
                <RefreshCw className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>刷新料仓</TooltipContent>
          </Tooltip>
        </div>
      </div>

          {/* 操作说明 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="sm" className="text-muted-foreground">
                <span className="text-xs">?</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent className="max-w-md">
              <div className="text-xs space-y-1">
                <div className="font-medium">操作说明：</div>
                <div>• 每个搅拌站有独立的配比控制</div>
                <div>• 料仓管理用于设置存储位置</div>
                <div>• 启用料仓时必须勾选"启用"</div>
                <div>• 物料分配可设置为平均或按容量</div>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
    </div>
  );
}
