'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { 
  History, 
  Settings, 
  RefreshCw, 
  Printer,
  Database,
  Users,
  Calculator
} from 'lucide-react';

import type { MixingStation } from '@/types/mixRatio';

interface MixRatioToolbarProps {
  unifiedRatio: boolean;
  onUnifiedRatioChange: (checked: boolean) => void;
  selectedStation: string;
  onStationChange: (stationId: string) => void;
  stations: MixingStation[];
  selectedRatioCode: string;
  onRatioCodeChange: (code: string) => void;
  onOpenHistory: () => void;
  onOpenMaterialManagement: () => void;
  onOpenSiloManagement: () => void;
  onOpenQualityStandard: () => void;
  onOpenParameterSettings: () => void;
  onRefreshSilos: () => void;
}

export function MixRatioToolbar({
  unifiedRatio,
  onUnifiedRatioChange,
  selectedStation,
  onStationChange,
  stations,
  selectedRatioCode,
  onRatioCodeChange,
  onOpenHistory,
  onOpenMaterialManagement,
  onOpenSiloManagement,
  onOpenQualityStandard,
  onOpenParameterSettings,
  onRefreshSilos,
}: MixRatioToolbarProps) {
  
  // 模拟配比编号列表
  const ratioCodes = [
    'C125-00003',
    'C125-00004', 
    'C125-00005',
    'C130-00001',
    'C130-00002'
  ];

  return (
    <div className="border-b bg-muted/30 p-4">
      <div className="flex items-center justify-between">
        {/* 左侧控件 */}
        <div className="flex items-center space-x-6">
          {/* 砼配比标题 */}
          <div className="text-2xl font-bold text-primary bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            砼配比
          </div>

          {/* 所有站统一配比 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="unified-ratio"
              checked={unifiedRatio}
              onCheckedChange={onUnifiedRatioChange}
            />
            <Label htmlFor="unified-ratio" className="text-sm font-medium">
              所有站统一配比
            </Label>
          </div>

          {/* 搅拌站选择 */}
          <div className="flex items-center space-x-2">
            <Label className="text-sm font-medium">搅拌站:</Label>
            <Select value={selectedStation} onValueChange={onStationChange}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="选择搅拌站" />
              </SelectTrigger>
              <SelectContent>
                {stations.map((station) => (
                  <SelectItem key={station.id} value={station.id}>
                    {station.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 搅拌站配比按钮 */}
          <Button variant="outline" size="sm">
            {stations.find(s => s.id === selectedStation)?.name || '搅拌站'}配比
          </Button>

          {/* 打印按钮 */}
          <Button variant="outline" size="sm">
            <Printer className="mr-2 h-4 w-4" />
            打印
          </Button>
        </div>

        {/* 右侧控件 */}
        <div className="flex items-center space-x-4">
          {/* 料仓管理 */}
          <Button variant="outline" size="sm" onClick={onOpenSiloManagement}>
            <Database className="mr-2 h-4 w-4" />
            料仓
          </Button>

          {/* 材料名称管理 */}
          <Button variant="outline" size="sm" onClick={onOpenMaterialManagement}>
            <Users className="mr-2 h-4 w-4" />
            材料名称管理
          </Button>

          {/* 存放地名称管理 */}
          <Button variant="outline" size="sm" onClick={onOpenSiloManagement}>
            <Database className="mr-2 h-4 w-4" />
            存放地名称管理
          </Button>

          {/* 历史记录 */}
          <Button variant="outline" size="sm" onClick={onOpenHistory}>
            <History className="mr-2 h-4 w-4" />
            历史
          </Button>

          {/* 砂浆配比 */}
          <Button variant="outline" size="sm">
            <Calculator className="mr-2 h-4 w-4" />
            砂浆配比
          </Button>

          {/* 设置 */}
          <Button variant="outline" size="sm" onClick={onOpenParameterSettings}>
            <Settings className="mr-2 h-4 w-4" />
            设置
          </Button>

          {/* 配比编号选择 */}
          <div className="flex items-center space-x-2">
            <Label className="text-sm font-medium">配比编号:</Label>
            <Select value={selectedRatioCode} onValueChange={onRatioCodeChange}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="选择配比" />
              </SelectTrigger>
              <SelectContent>
                {ratioCodes.map((code) => (
                  <SelectItem key={code} value={code}>
                    {code}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 刷新料仓 */}
          <Button variant="outline" size="sm" onClick={onRefreshSilos}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新料仓
          </Button>
        </div>
      </div>

      {/* 操作说明 */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="text-sm text-blue-800">
          <div className="font-medium mb-2">操作说明：</div>
          <div className="space-y-1 text-xs">
            <div>1、本窗口针对每个搅拌站有不同的控制（通过左上角来选择不同的搅拌站），调度安排发车时要检测该配置，判断生产任务是否可以发送到对应搅拌站；</div>
            <div>2、右上角的"存放地名称管理"用于给料仓起名字，方便过磅时过磅员来选择卸料地，具体对应关系各个单位不一致；</div>
            <div>3、一般情况现在的搅拌站都是料仓单独控制，所以填写内容是只填写第一部分即可，后面两部分是为了应对以前的旧站有的操作台上有一个切换开关来控制使用哪一个水泥罐，而在程序中输入配比时使用的是同一个输入方法的情况而设立的；</div>
            <div>4、如果有两个料仓的配置信息一致，那么将配比中物料按照事先设定好的方式分配物料，这个参数可由系统管理员来设置：A)、平均分配物料；B)、根据料仓上限来分配物料；</div>
            <div>5、启用料仓时，请一定要将后面的"启用"画上√，否则不生效；</div>
          </div>
        </div>
      </div>
    </div>
  );
}
