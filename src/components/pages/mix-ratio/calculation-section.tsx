'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calculator, FileText, RotateCcw, CheckCircle } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

import type { MixRatio, Material, CalculationMethod } from '@/types/mixRatio';

interface CalculationSectionProps {
  mixRatio: MixRatio;
  onUpdate: (mixRatio: MixRatio) => void;
  materials: Material[];
}

export function CalculationSection({ mixRatio, onUpdate, materials }: CalculationSectionProps) {
  const [isCalculating, setIsCalculating] = useState(false);

  const handleParamChange = (field: keyof typeof mixRatio.calculationParams, value: number) => {
    onUpdate({
      ...mixRatio,
      calculationParams: {
        ...mixRatio.calculationParams,
        [field]: value
      }
    });
  };

  const handleMethodChange = (method: CalculationMethod) => {
    onUpdate({
      ...mixRatio,
      calculationMethod: method
    });
  };

  // 反算功能 - 根据配比计算书算法
  const handleReverseCalculation = async () => {
    setIsCalculating(true);
    
    try {
      // 模拟计算过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const { calculationParams, calculationMethod } = mixRatio;
      const { density, waterCementRatio, waterContent, sandRatio } = calculationParams;
      
      // 基础计算
      const totalWeight = density * 1000; // kg/m³
      const cementContent = waterContent / waterCementRatio; // 水泥用量
      const aggregateContent = totalWeight - waterContent - cementContent; // 骨料总量
      const sandContent = aggregateContent * (sandRatio / 100); // 砂用量
      const stoneContent = aggregateContent - sandContent; // 石子用量
      
      // 外加剂计算
      const additiveContent = cementContent * (calculationParams.additiveRatio / 100);
      
      // 更新配比项目
      const updatedItems = mixRatio.items.map(item => {
        let theoreticalAmount = 0;
        
        switch (item.materialName) {
          case '水泥':
            theoreticalAmount = Math.round(cementContent);
            break;
          case '水':
            theoreticalAmount = Math.round(waterContent);
            break;
          case '沙子':
            theoreticalAmount = Math.round(sandContent);
            break;
          case '石子':
            theoreticalAmount = Math.round(stoneContent);
            break;
          case '外加剂':
            theoreticalAmount = Math.round(additiveContent);
            break;
          default:
            theoreticalAmount = item.theoreticalAmount;
        }
        
        return {
          ...item,
          theoreticalAmount,
          actualAmount: theoreticalAmount,
          designValue: theoreticalAmount
        };
      });
      
      // 更新计算参数
      const updatedParams = {
        ...calculationParams,
        totalWeight: Math.round(totalWeight),
        mixingTime: Math.round(60 + (totalWeight - 2000) / 50) // 简单的搅拌时间计算
      };
      
      onUpdate({
        ...mixRatio,
        calculationParams: updatedParams,
        items: updatedItems
      });
      
    } catch (error) {
      console.error('计算失败:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleApplyCalculation = () => {
    // 应用计算结果到配比表格
    console.log('应用计算结果');
  };

  const calculationMethods = [
    { value: 'no_flyash_excess', label: '不考虑粉煤灰超量系数' },
    { value: 'consider_flyash', label: '考虑粉煤灰系数' },
    { value: 'flyash_volume_conversion', label: '考虑粉煤灰系数且体积折算' },
    { value: 'excess_flyash', label: '超量煤灰' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Badge variant="outline">二</Badge>
          计算
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 基础参数 */}
        <div className="grid grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">密度</Label>
            <div className="flex items-center space-x-1">
              <Input
                type="number"
                step="0.01"
                value={mixRatio.calculationParams.density}
                onChange={(e) => handleParamChange('density', Number(e.target.value))}
                className="h-8"
              />
              <span className="text-xs text-muted-foreground">t/m³</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label className="text-sm font-medium">总重量</Label>
            <div className="flex items-center space-x-1">
              <Input
                type="number"
                value={mixRatio.calculationParams.totalWeight}
                onChange={(e) => handleParamChange('totalWeight', Number(e.target.value))}
                className="h-8"
                readOnly
              />
              <span className="text-xs text-muted-foreground">kg</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label className="text-sm font-medium">搅拌时间</Label>
            <div className="flex items-center space-x-1">
              <Input
                type="number"
                value={mixRatio.calculationParams.mixingTime}
                onChange={(e) => handleParamChange('mixingTime', Number(e.target.value))}
                className="h-8"
              />
              <span className="text-xs text-muted-foreground">秒</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <Button variant="outline" size="sm" className="mt-6">
              <FileText className="mr-2 h-4 w-4" />
              打开配比计算书
            </Button>
          </div>
        </div>

        <Separator />

        {/* 计算规则表单 */}
        <div className="space-y-4">
          <div className="text-sm font-medium">计算规则表单</div>
          
          {/* 参数输入 */}
          <div className="grid grid-cols-6 gap-3">
            <div className="space-y-1">
              <Label className="text-xs">水胶比</Label>
              <Input
                type="number"
                step="0.01"
                value={mixRatio.calculationParams.waterCementRatio}
                onChange={(e) => handleParamChange('waterCementRatio', Number(e.target.value))}
                className="h-7 text-xs"
              />
            </div>
            
            <div className="space-y-1">
              <Label className="text-xs">用水量</Label>
              <Input
                type="number"
                value={mixRatio.calculationParams.waterContent}
                onChange={(e) => handleParamChange('waterContent', Number(e.target.value))}
                className="h-7 text-xs"
              />
            </div>
            
            <div className="space-y-1">
              <Label className="text-xs">沙率%</Label>
              <Input
                type="number"
                value={mixRatio.calculationParams.sandRatio}
                onChange={(e) => handleParamChange('sandRatio', Number(e.target.value))}
                className="h-7 text-xs"
              />
            </div>
            
            <div className="space-y-1">
              <Label className="text-xs">外加剂%</Label>
              <Input
                type="number"
                step="0.1"
                value={mixRatio.calculationParams.additiveRatio}
                onChange={(e) => handleParamChange('additiveRatio', Number(e.target.value))}
                className="h-7 text-xs"
              />
            </div>
            
            <div className="space-y-1">
              <Label className="text-xs">防冻剂%</Label>
              <Input
                type="number"
                step="0.1"
                value={mixRatio.calculationParams.antifreezeRatio}
                onChange={(e) => handleParamChange('antifreezeRatio', Number(e.target.value))}
                className="h-7 text-xs"
              />
            </div>
            
            <div className="space-y-1">
              <Label className="text-xs">粉煤灰%</Label>
              <Input
                type="number"
                step="0.1"
                value={mixRatio.calculationParams.flyashRatio}
                onChange={(e) => handleParamChange('flyashRatio', Number(e.target.value))}
                className="h-7 text-xs"
              />
            </div>
          </div>

          {/* 计算方法选择 */}
          <div className="space-y-2">
            <Label className="text-sm">计算方法</Label>
            <Select value={mixRatio.calculationMethod} onValueChange={handleMethodChange}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {calculationMethods.map((method) => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 分配比例和操作 */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              分配比例设置完成后，点击反算按钮计算准确配比
            </div>

            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                onClick={handleReverseCalculation}
                disabled={isCalculating}
              >
                <Calculator className="mr-2 h-4 w-4" />
                {isCalculating ? '计算中...' : '反算'}
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={handleApplyCalculation}
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                应用
              </Button>
            </div>
          </div>


        </div>
      </CardContent>
    </Card>
  );
}
