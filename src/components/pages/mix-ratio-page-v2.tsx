'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Calculator,
  X,
  Save,
  Send,
  Settings,
  History,
  Database,
  Users,
  Plus,
  Minus,
  GripVertical,
  Printer,
  RefreshCw,
  CheckCircle,
  FileText,
  Zap,
  Copy,
  Trash2,
  Edit
} from 'lucide-react';

import { MaterialManagementV2 } from './mix-ratio/material-management-v2';
import { SiloManagementV2 } from './mix-ratio/silo-management-v2';
import { RatioHistoryV2 } from './mix-ratio/ratio-history-v2';
import { ParameterSettingsV2 } from './mix-ratio/parameter-settings-v2';
import { RatioCalculationEngine } from './mix-ratio/ratio-calculation-engine';
import { RatioTemplateManager } from './mix-ratio/ratio-template-manager';
import { QualityInspector } from './mix-ratio/quality-inspector';
import { RatioReportGenerator } from './mix-ratio/ratio-report-generator';
import type { Task } from '@/types';

interface MixRatioPageV2Props {
  task: Task;
  onClose: () => void;
}

interface MaterialItem {
  id: string;
  name: string;
  type: 'aggregate' | 'powder' | 'water' | 'additive';
  amount: number;
  waterContent: number;
  actualAmount: number;
  siloName: string;
}

interface CalculationParams {
  density: number;
  waterRatio: number;
  waterAmount: number;
  sandRatio: number;
  additiveRatio: number;
  antifreezeRatio: number;
  flyashRatio: number;
  totalWeight: number;
  mixingTime: number;
}

interface QuickRatioTemplate {
  id: string;
  name: string;
  strength: string;
  params: CalculationParams;
  materials: MaterialItem[];
}

interface SiloInfo {
  id: string;
  name: string;
  type: 'aggregate' | 'powder' | 'water' | 'additive';
  capacity: number;
  current: number;
  isEnabled: boolean;
  identifier: string;
}

export function MixRatioPageV2({ task, onClose }: MixRatioPageV2Props) {
  const [rightPanelWidth, setRightPanelWidth] = useState(280);
  const [isDragging, setIsDragging] = useState(false);
  const [selectedStation, setSelectedStation] = useState('station1');
  const [unifiedRatio, setUnifiedRatio] = useState(false);
  const [selectedRatioCode, setSelectedRatioCode] = useState('C30-001');
  const [isSync, setIsSync] = useState(false);
  const [productionTips, setProductionTips] = useState('');
  const [serialNumber, setSerialNumber] = useState('');

  // 任务信息状态
  const [taskInfo, setTaskInfo] = useState({
    strength: task.strength,
    slump: '180',
    impermeability: '',
    freezeResistance: '',
    pouringMethod: task.pouringMethod
  });

  // 模态框状态
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [isSiloModalOpen, setIsSiloModalOpen] = useState(false);
  const [isQuickRatioModalOpen, setIsQuickRatioModalOpen] = useState(false);
  const [isParameterModalOpen, setIsParameterModalOpen] = useState(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [isQualityModalOpen, setIsQualityModalOpen] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isCalculationEngineOpen, setIsCalculationEngineOpen] = useState(false);
  
  // 计算参数
  const [calcParams, setCalcParams] = useState<CalculationParams>({
    density: 2.36,
    waterRatio: 0.45,
    waterAmount: 180,
    sandRatio: 35,
    additiveRatio: 1.3,
    antifreezeRatio: 0,
    flyashRatio: 0,
    totalWeight: 2360,
    mixingTime: 95
  });

  // 快速配比模板
  const [quickRatioTemplates] = useState<QuickRatioTemplate[]>([
    {
      id: '1',
      name: 'C30标准配比',
      strength: 'C30',
      params: {
        density: 2.36,
        waterRatio: 0.45,
        waterAmount: 180,
        sandRatio: 35,
        additiveRatio: 1.3,
        antifreezeRatio: 0,
        flyashRatio: 0,
        totalWeight: 2360,
        mixingTime: 95
      },
      materials: [
        { id: '1', name: '水泥', type: 'powder', amount: 400, waterContent: 0, actualAmount: 400, siloName: '1#水泥罐' },
        { id: '2', name: '水', type: 'water', amount: 180, waterContent: 0, actualAmount: 180, siloName: '1#水罐' },
        { id: '3', name: '中砂', type: 'aggregate', amount: 650, waterContent: 3.5, actualAmount: 627, siloName: '2#砂罐' },
        { id: '4', name: '石子', type: 'aggregate', amount: 1100, waterContent: 1.0, actualAmount: 1089, siloName: '3#石罐' },
        { id: '5', name: '减水剂', type: 'additive', amount: 5.2, waterContent: 0, actualAmount: 5.2, siloName: '1#外加剂' }
      ]
    },
    {
      id: '2',
      name: 'C25经济配比',
      strength: 'C25',
      params: {
        density: 2.35,
        waterRatio: 0.50,
        waterAmount: 175,
        sandRatio: 38,
        additiveRatio: 1.0,
        antifreezeRatio: 0,
        flyashRatio: 15,
        totalWeight: 2350,
        mixingTime: 90
      },
      materials: [
        { id: '1', name: '水泥', type: 'powder', amount: 300, waterContent: 0, actualAmount: 300, siloName: '1#水泥罐' },
        { id: '2', name: '粉煤灰', type: 'powder', amount: 50, waterContent: 0, actualAmount: 50, siloName: '2#粉煤灰' },
        { id: '3', name: '水', type: 'water', amount: 175, waterContent: 0, actualAmount: 175, siloName: '1#水罐' },
        { id: '4', name: '中砂', type: 'aggregate', amount: 680, waterContent: 3.0, actualAmount: 660, siloName: '2#砂罐' },
        { id: '5', name: '石子', type: 'aggregate', amount: 1110, waterContent: 1.0, actualAmount: 1099, siloName: '3#石罐' },
        { id: '6', name: '减水剂', type: 'additive', amount: 3.5, waterContent: 0, actualAmount: 3.5, siloName: '1#外加剂' }
      ]
    }
  ]);

  // 配比材料
  const [materials, setMaterials] = useState<MaterialItem[]>([
    { id: '1', name: '水泥', type: 'powder', amount: 400, waterContent: 0, actualAmount: 400, siloName: '1#水泥罐' },
    { id: '2', name: '水', type: 'water', amount: 180, waterContent: 0, actualAmount: 180, siloName: '1#水罐' },
    { id: '3', name: '中砂', type: 'aggregate', amount: 650, waterContent: 3.5, actualAmount: 627, siloName: '2#砂罐' },
    { id: '4', name: '石子', type: 'aggregate', amount: 1100, waterContent: 1.0, actualAmount: 1089, siloName: '3#石罐' },
    { id: '5', name: '减水剂', type: 'additive', amount: 5.2, waterContent: 0, actualAmount: 5.2, siloName: '1#外加剂' }
  ]);

  // 料仓信息
  const [silos, setSilos] = useState<SiloInfo[]>([
    { id: '1', name: '1#水泥罐', type: 'powder', capacity: 100, current: 85, isEnabled: true, identifier: '粉料1' },
    { id: '2', name: '2#粉煤灰', type: 'powder', capacity: 80, current: 60, isEnabled: true, identifier: '粉料2' },
    { id: '3', name: '2#砂罐', type: 'aggregate', capacity: 200, current: 150, isEnabled: true, identifier: '骨料1' },
    { id: '4', name: '3#石罐', type: 'aggregate', capacity: 200, current: 180, isEnabled: true, identifier: '骨料2' },
    { id: '5', name: '1#水罐', type: 'water', capacity: 50, current: 45, isEnabled: true, identifier: '水1' },
    { id: '6', name: '1#外加剂', type: 'additive', capacity: 10, current: 8, isEnabled: true, identifier: '外加剂1' },
    { id: '7', name: '2#外加剂', type: 'additive', capacity: 10, current: 5, isEnabled: false, identifier: '外加剂2' }
  ]);

  // 配比编号列表
  const ratioCodes = ['C30-001', 'C30-002', 'C25-001', 'C25-002', 'C35-001'];

  // 搅拌站列表
  const stations = [
    { id: 'station1', name: '搅拌站A', code: 'STA' },
    { id: 'station2', name: '搅拌站B', code: 'STB' }
  ];

  // 拖拽调整右侧面板宽度
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      const newWidth = window.innerWidth - e.clientX;
      setRightPanelWidth(Math.max(200, Math.min(400, newWidth)));
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  // 反算功能
  const handleCalculate = () => {
    const { density, waterRatio, waterAmount, sandRatio, additiveRatio, flyashRatio } = calcParams;
    const totalWeight = density * 1000;
    const cementAmount = waterAmount / waterRatio * (1 - flyashRatio / 100);
    const flyashAmount = waterAmount / waterRatio * (flyashRatio / 100);
    const aggregateTotal = totalWeight - waterAmount - cementAmount - flyashAmount;
    const sandAmount = aggregateTotal * (sandRatio / 100);
    const stoneAmount = aggregateTotal - sandAmount;
    const additiveAmount = (cementAmount + flyashAmount) * (additiveRatio / 100);

    // 更新总重量和搅拌时间
    setCalcParams(prev => ({
      ...prev,
      totalWeight: Math.round(totalWeight),
      mixingTime: Math.round(60 + (totalWeight - 2000) / 50)
    }));

    // 更新材料用量
    setMaterials(prev => prev.map(item => {
      switch (item.type) {
        case 'powder':
          if (item.name.includes('水泥')) {
            return { ...item, amount: Math.round(cementAmount), actualAmount: Math.round(cementAmount) };
          } else if (item.name.includes('粉煤灰')) {
            return { ...item, amount: Math.round(flyashAmount), actualAmount: Math.round(flyashAmount) };
          }
          return item;
        case 'water':
          return { ...item, amount: Math.round(waterAmount), actualAmount: Math.round(waterAmount) };
        case 'aggregate':
          if (item.name.includes('砂')) {
            return { ...item, amount: Math.round(sandAmount), actualAmount: Math.round(sandAmount * (1 - item.waterContent / 100)) };
          } else {
            return { ...item, amount: Math.round(stoneAmount), actualAmount: Math.round(stoneAmount * (1 - item.waterContent / 100)) };
          }
        case 'additive':
          return { ...item, amount: Math.round(additiveAmount * 10) / 10, actualAmount: Math.round(additiveAmount * 10) / 10 };
        default:
          return item;
      }
    }));
  };

  // 快速应用配比模板
  const applyQuickRatio = (template: QuickRatioTemplate) => {
    setCalcParams(template.params);
    setMaterials(template.materials);
    setIsQuickRatioModalOpen(false);
  };

  // 保存配比
  const handleSave = () => {
    console.log('保存配比:', { calcParams, materials, task });
    // TODO: 调用API保存
  };

  // 审核发送
  const handleApproveAndSend = () => {
    console.log('审核发送配比:', { calcParams, materials, task });
    // TODO: 调用API审核发送
  };

  // 添加材料
  const addMaterial = (type: MaterialItem['type']) => {
    const newMaterial: MaterialItem = {
      id: Date.now().toString(),
      name: '',
      type,
      amount: 0,
      waterContent: 0,
      actualAmount: 0,
      siloName: ''
    };
    setMaterials(prev => [...prev, newMaterial]);
  };

  // 删除材料
  const removeMaterial = (id: string) => {
    setMaterials(prev => prev.filter(m => m.id !== id));
  };

  // 复制配比
  const copyRatio = () => {
    const ratioData = { calcParams, materials };
    navigator.clipboard.writeText(JSON.stringify(ratioData, null, 2));
    console.log('配比已复制到剪贴板');
  };

  const updateMaterial = (id: string, field: keyof MaterialItem, value: any) => {
    setMaterials(prev => prev.map(item => 
      item.id === id ? { ...item, [field]: value } : item
    ));
  };

  const getMaterialsByType = (type: string) => materials.filter(m => m.type === type);
  const getSilosByType = (type: string) => silos.filter(s => s.type === type);

  const materialTypes = [
    { key: 'powder', label: '粉料', color: 'bg-orange-100 text-orange-800' },
    { key: 'aggregate', label: '骨料', color: 'bg-gray-100 text-gray-800' },
    { key: 'water', label: '水', color: 'bg-blue-100 text-blue-800' },
    { key: 'additive', label: '外加剂', color: 'bg-green-100 text-green-800' }
  ];

  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col">
      {/* 顶部工具栏 */}
      <div className="h-10 border-b bg-muted/30 flex items-center justify-between px-3 flex-shrink-0">
        <div className="flex items-center space-x-3 text-xs">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={unifiedRatio}
              onCheckedChange={setUnifiedRatio}
              className="h-3 w-3"
            />
            <Label className="text-xs">统一配比</Label>
          </div>

          <span className="font-medium">{task.taskNumber}</span>
          <span className="text-muted-foreground">{task.projectName}</span>
          <span className="text-muted-foreground">{task.strength}</span>

          <Select value={selectedStation} onValueChange={setSelectedStation}>
            <SelectTrigger className="h-6 w-20 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {stations.map(station => (
                <SelectItem key={station.id} value={station.id}>
                  {station.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedRatioCode} onValueChange={setSelectedRatioCode}>
            <SelectTrigger className="h-6 w-24 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {ratioCodes.map(code => (
                <SelectItem key={code} value={code}>
                  {code}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsQuickRatioModalOpen(true)} title="快速配比">
            <Zap className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsTemplateModalOpen(true)} title="配比模板">
            <FileText className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsCalculationEngineOpen(true)} title="智能计算">
            <Calculator className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsQualityModalOpen(true)} title="质量检验">
            <CheckCircle className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsHistoryModalOpen(true)} title="历史记录">
            <History className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsMaterialModalOpen(true)} title="材料管理">
            <Users className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsSiloModalOpen(true)} title="料仓管理">
            <Database className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsParameterModalOpen(true)} title="参数设置">
            <Settings className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => setIsReportModalOpen(true)} title="生成报告">
            <Printer className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={copyRatio} title="复制配比">
            <Copy className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 px-2" onClick={onClose}>
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧主要内容 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-y-auto p-2 space-y-2">
            
            {/* 任务信息 */}
            <Card>
              <CardContent className="p-2">
                <div className="grid grid-cols-6 gap-2 text-xs">
                  <div>
                    <Label className="text-xs text-muted-foreground">任务编号</Label>
                    <Input value={task.taskNumber} className="h-5 text-xs" readOnly />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">强度</Label>
                    <Input
                      value={taskInfo.strength}
                      className="h-5 text-xs"
                      onChange={(e) => setTaskInfo(prev => ({ ...prev, strength: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">坍落度</Label>
                    <Input
                      value={taskInfo.slump}
                      className="h-5 text-xs"
                      onChange={(e) => setTaskInfo(prev => ({ ...prev, slump: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">抗渗</Label>
                    <Input
                      value={taskInfo.impermeability}
                      placeholder="P6"
                      className="h-5 text-xs"
                      onChange={(e) => setTaskInfo(prev => ({ ...prev, impermeability: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">抗冻</Label>
                    <Input
                      value={taskInfo.freezeResistance}
                      placeholder="F100"
                      className="h-5 text-xs"
                      onChange={(e) => setTaskInfo(prev => ({ ...prev, freezeResistance: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">浇筑方式</Label>
                    <Input value={taskInfo.pouringMethod} className="h-5 text-xs" readOnly />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 计算参数 - 紧凑布局 */}
            <Card>
              <CardContent className="p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-medium">计算参数</span>
                  <div className="flex space-x-1">
                    <Button size="sm" onClick={handleCalculate} className="h-6 px-3 text-xs">
                      <Calculator className="mr-1 h-3 w-3" />
                      反算
                    </Button>
                    <Button size="sm" variant="outline" className="h-6 px-3 text-xs">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      应用
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-8 gap-2 text-xs">
                  <div>
                    <Label className="text-xs text-muted-foreground">密度</Label>
                    <Input
                      type="number"
                      step="0.01"
                      value={calcParams.density}
                      onChange={(e) => setCalcParams(prev => ({ ...prev, density: Number(e.target.value) }))}
                      className="h-5 text-xs"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">水胶比</Label>
                    <Input
                      type="number"
                      step="0.01"
                      value={calcParams.waterRatio}
                      onChange={(e) => setCalcParams(prev => ({ ...prev, waterRatio: Number(e.target.value) }))}
                      className="h-5 text-xs"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">用水量</Label>
                    <Input
                      type="number"
                      value={calcParams.waterAmount}
                      onChange={(e) => setCalcParams(prev => ({ ...prev, waterAmount: Number(e.target.value) }))}
                      className="h-5 text-xs"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">砂率%</Label>
                    <Input
                      type="number"
                      value={calcParams.sandRatio}
                      onChange={(e) => setCalcParams(prev => ({ ...prev, sandRatio: Number(e.target.value) }))}
                      className="h-5 text-xs"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">外加剂%</Label>
                    <Input
                      type="number"
                      step="0.1"
                      value={calcParams.additiveRatio}
                      onChange={(e) => setCalcParams(prev => ({ ...prev, additiveRatio: Number(e.target.value) }))}
                      className="h-5 text-xs"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">粉煤灰%</Label>
                    <Input
                      type="number"
                      step="0.1"
                      value={calcParams.flyashRatio}
                      onChange={(e) => setCalcParams(prev => ({ ...prev, flyashRatio: Number(e.target.value) }))}
                      className="h-5 text-xs"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">总重量</Label>
                    <Input
                      type="number"
                      value={calcParams.totalWeight}
                      className="h-5 text-xs"
                      readOnly
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-muted-foreground">搅拌时间</Label>
                    <Input
                      type="number"
                      value={calcParams.mixingTime}
                      onChange={(e) => setCalcParams(prev => ({ ...prev, mixingTime: Number(e.target.value) }))}
                      className="h-5 text-xs"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 配比表格 - 按材料类型分组 */}
            {materialTypes
              .filter(({ key }) => getMaterialsByType(key).length > 0)
              .map(({ key, label, color }) => {
              const typeMaterials = getMaterialsByType(key);
              
              return (
                <Card key={key}>
                  <CardContent className="p-2">
                    <div className="flex items-center justify-between mb-2">
                      <Badge className={`text-xs ${color}`}>{label}</Badge>
                      <Button variant="ghost" size="sm" className="h-5 px-2">
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <div className="space-y-1">
                      {typeMaterials.map((material) => (
                        <div key={material.id} className="grid grid-cols-7 gap-1 items-center text-xs">
                          <div className="font-medium">{material.name}</div>
                          <div className="flex items-center space-x-1">
                            <Input
                              type="number"
                              value={material.amount}
                              onChange={(e) => updateMaterial(material.id, 'amount', Number(e.target.value))}
                              className="h-5 text-xs"
                            />
                            <span className="text-xs text-muted-foreground">kg</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Input
                              type="number"
                              step="0.1"
                              value={material.waterContent}
                              onChange={(e) => updateMaterial(material.id, 'waterContent', Number(e.target.value))}
                              className="h-5 text-xs"
                            />
                            <span className="text-xs text-muted-foreground">%</span>
                          </div>
                          <div className="text-xs font-medium">{material.actualAmount}</div>
                          <Select value={material.siloName} onValueChange={(value) => updateMaterial(material.id, 'siloName', value)}>
                            <SelectTrigger className="h-5 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {getSilosByType(key).map((silo) => (
                                <SelectItem key={silo.id} value={silo.name}>
                                  {silo.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <div className="flex space-x-1">
                            <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                              <Plus className="h-2 w-2" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                              <Minus className="h-2 w-2" />
                            </Button>
                          </div>
                          <Button variant="ghost" size="sm" className="h-4 w-4 p-0 text-red-500">
                            <X className="h-2 w-2" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* 底部操作栏 */}
          <div className="border-t bg-muted/30 p-2 flex items-center justify-between flex-shrink-0">
            <div className="flex items-center space-x-4 text-xs">
              <div className="text-muted-foreground">
                总重量: {materials.reduce((sum, m) => sum + m.amount, 0)} kg
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={isSync}
                  onCheckedChange={setIsSync}
                  className="h-3 w-3"
                />
                <Label className="text-xs">同步选用配比</Label>
              </div>
              <div className="flex items-center space-x-1">
                <Label className="text-xs">生产提示:</Label>
                <Input
                  value={productionTips}
                  onChange={(e) => setProductionTips(e.target.value)}
                  placeholder="输入生产提示"
                  className="h-5 w-32 text-xs"
                />
              </div>
              <div className="flex items-center space-x-1">
                <Label className="text-xs">流水号:</Label>
                <Input
                  value={serialNumber}
                  onChange={(e) => setSerialNumber(e.target.value)}
                  placeholder="自动生成"
                  className="h-5 w-24 text-xs"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              <Button size="sm" onClick={handleSave} className="h-6 px-3 text-xs">
                <Save className="mr-1 h-3 w-3" />
                保存
              </Button>
              <Button size="sm" onClick={handleApproveAndSend} className="h-6 px-3 text-xs">
                <Send className="mr-1 h-3 w-3" />
                审核发送
              </Button>
            </div>
          </div>
        </div>

        {/* 拖拽分隔条 */}
        <div
          className="w-1 bg-border hover:bg-primary/20 cursor-col-resize flex items-center justify-center"
          onMouseDown={handleMouseDown}
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>

        {/* 右侧料仓信息 */}
        <div 
          className="border-l bg-muted/10 flex flex-col overflow-hidden"
          style={{ width: rightPanelWidth }}
        >
          <div className="p-2 border-b">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium">料仓状态</span>
              <Button variant="ghost" size="sm" className="h-5 px-2" title="管理料仓">
                <Database className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-2 space-y-2">
            {materialTypes
              .filter(({ key }) => getSilosByType(key).length > 0)
              .map(({ key, label, color }) => {
              const typeSilos = getSilosByType(key);
              
              return (
                <div key={key}>
                  <Badge className={`text-xs mb-1 ${color}`}>{label}</Badge>
                  <div className="space-y-1">
                    {typeSilos.map((silo) => (
                      <div key={silo.id} className="bg-background rounded p-2 text-xs">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium">{silo.name}</span>
                          <span className="text-muted-foreground">{silo.current}t</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-1">
                          <div 
                            className="bg-primary h-1 rounded-full" 
                            style={{ width: `${(silo.current / silo.capacity) * 100}%` }}
                          />
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {silo.current}/{silo.capacity}t ({Math.round((silo.current / silo.capacity) * 100)}%)
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* 快速配比模态框 */}
      <Dialog open={isQuickRatioModalOpen} onOpenChange={setIsQuickRatioModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              快速配比模板
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4">
            {quickRatioTemplates.map((template) => (
              <Card key={template.id} className="cursor-pointer hover:bg-muted/50" onClick={() => applyQuickRatio(template)}>
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{template.name}</h3>
                      <Badge>{template.strength}</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <div>密度: {template.params.density} t/m³</div>
                      <div>水胶比: {template.params.waterRatio}</div>
                      <div>用水量: {template.params.waterAmount} kg</div>
                      <div>材料: {template.materials.length} 种</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* 历史记录模态框 */}
      <Dialog open={isHistoryModalOpen} onOpenChange={setIsHistoryModalOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              配比历史记录
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <RatioHistoryV2
              onClose={() => setIsHistoryModalOpen(false)}
              onApplyRatio={(record) => {
                setCalcParams(record.parameters);
                setMaterials(record.materials);
                setIsHistoryModalOpen(false);
              }}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* 材料管理模态框 */}
      <Dialog open={isMaterialModalOpen} onOpenChange={setIsMaterialModalOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              材料管理
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <MaterialManagementV2
              onClose={() => setIsMaterialModalOpen(false)}
              onMaterialsChange={(materials) => console.log('材料更新:', materials)}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* 料仓管理模态框 */}
      <Dialog open={isSiloModalOpen} onOpenChange={setIsSiloModalOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              料仓管理
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <SiloManagementV2
              onClose={() => setIsSiloModalOpen(false)}
              onSilosChange={(newSilos) => setSilos(newSilos)}
              currentStation={selectedStation}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* 参数设置模态框 */}
      <Dialog open={isParameterModalOpen} onOpenChange={setIsParameterModalOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              参数设置
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <ParameterSettingsV2
              onClose={() => setIsParameterModalOpen(false)}
              onSettingsChange={(settings) => console.log('参数更新:', settings)}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* 配比模板管理模态框 */}
      <Dialog open={isTemplateModalOpen} onOpenChange={setIsTemplateModalOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              配比模板管理
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <RatioTemplateManager
              onClose={() => setIsTemplateModalOpen(false)}
              onApplyTemplate={(template) => {
                setCalcParams(template.parameters);
                setMaterials(template.materials);
                setIsTemplateModalOpen(false);
              }}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* 智能计算引擎模态框 */}
      <Dialog open={isCalculationEngineOpen} onOpenChange={setIsCalculationEngineOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              智能配比计算引擎
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <RatioCalculationEngine
              params={{
                ...calcParams,
                targetStrength: parseInt(task.strength.replace('C', '')),
                slump: parseInt(taskInfo.slump),
                temperature: 20
              }}
              materials={materials}
              onResultChange={(result) => {
                setMaterials(result.materials);
                console.log('计算结果:', result);
              }}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* 质量检验模态框 */}
      <Dialog open={isQualityModalOpen} onOpenChange={setIsQualityModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              质量检验
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <QualityInspector
              parameters={{
                ...calcParams,
                targetStrength: parseInt(task.strength.replace('C', '')),
                slump: parseInt(taskInfo.slump),
                temperature: 20
              }}
              materials={materials}
              onQualityChange={(score, issues) => {
                console.log('质量评分:', score, '问题:', issues);
              }}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* 报告生成模态框 */}
      <Dialog open={isReportModalOpen} onOpenChange={setIsReportModalOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Printer className="h-5 w-5" />
              生成配比报告
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-auto">
            <RatioReportGenerator
              data={{
                taskInfo: {
                  taskNumber: task.taskNumber,
                  projectName: task.projectName,
                  strength: task.strength,
                  slump: taskInfo.slump,
                  impermeability: taskInfo.impermeability,
                  freezeResistance: taskInfo.freezeResistance
                },
                parameters: calcParams,
                materials: materials,
                qualityScore: 85,
                strengthPrediction: parseInt(task.strength.replace('C', '')),
                totalCost: 180,
                carbonFootprint: 0.85,
                operator: '当前用户',
                station: stations.find(s => s.id === selectedStation)?.name || '搅拌站A',
                timestamp: new Date().toISOString()
              }}
              onClose={() => setIsReportModalOpen(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
