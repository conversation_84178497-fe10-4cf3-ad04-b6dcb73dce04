
// src/components/sections/task-list/task-item-details-row.tsx
'use client';

import React from 'react';
import type { Task } from '@/types';
import { cn } from '@/lib/utils';
import { Users, SigmaSquare, Truck, Construction, CalendarDays } from 'lucide-react';

interface TaskItemDetailsRowProps {
  task: Task;
  projectNameTextSize: string;
  baseTextSize: string;
  iconClasses: string;
  baseDetailClasses: string;
  detailSpacing: string;
}

export const TaskItemDetailsRow = React.memo(function TaskItemDetailsRow({
  task,
  projectNameTextSize,
  baseTextSize,
  iconClasses,
  baseDetailClasses,
  detailSpacing,
}: TaskItemDetailsRowProps) {
  return (
    <div className={cn(
      "flex items-baseline flex-1 min-w-0 overflow-x-auto custom-thin-horizontal-scrollbar",
      baseTextSize, detailSpacing
    )}>
      <span
        className={cn("font-semibold truncate mr-1 text-foreground", projectNameTextSize)}
        title={task.projectName}>
        {task.projectName}
      </span>
      <span className={cn(baseDetailClasses)} title={task.constructionUnit}>
        <Users className={iconClasses} />
        {task.constructionUnit}
      </span>
      <span className={cn(baseDetailClasses)} title={task.constructionSite}>
        <Construction className={iconClasses} />
        {task.constructionSite}
      </span>
      <span className={cn(baseDetailClasses)} title={`${task.supplyDate} ${task.supplyTime}`}>
        <CalendarDays className={iconClasses} />
        {task.supplyDate} {task.supplyTime}
      </span>
      <span className={cn("font-medium truncate text-foreground", baseTextSize)} title={`需供/完成: ${task.requiredVolume}/${task.completedVolume}m³`}>
        {task.completedVolume}/{task.requiredVolume}m³
      </span>
      <span className={cn("font-semibold truncate inline-flex items-center text-foreground", baseTextSize)} title={`${task.vehicleCount}车`}>
        <Truck className={iconClasses} />
        {task.vehicleCount}车
      </span>
      <span className={cn(baseDetailClasses)} title={task.pouringMethod}>
        <SigmaSquare className={iconClasses} />
        {task.pouringMethod}
      </span>
    </div>
  );
});

TaskItemDetailsRow.displayName = 'TaskItemDetailsRow';
