// src/components/sections/task-list/components/task-list-table-content.tsx

import React from 'react';
import type { ColumnDef, OnChangeFn, ColumnOrderState } from '@tanstack/react-table';
import { cn } from '@/lib/utils';
import type { Task, Vehicle, DensityStyleValues, TaskGroupConfig, TaskGroup } from '@/types';
import { VirtualizedTable } from '@/components/ui/virtualized-table';
import { OptimizedGroupTable } from './optimized-group-table';
import { TaskGroupHeader } from './task-group-header';

interface TaskListTableContentProps {
  // Data
  tasks: Task[];
  taskGroups: TaskGroup[];
  tableColumns: ColumnDef<Task>[];

  // Settings
  enableZebraStriping: boolean;
  densityStyles: DensityStyleValues;
  groupConfig: TaskGroupConfig;

  // Dimensions
  tableTotalWidth: number;
  estimateRowHeight: (task?: Task) => number;

  // Table State
  columnSizing: Record<string, number>;
  columnVisibility: Record<string, boolean>;
  columnOrder: string[];

  // Event Handlers
  onColumnSizingChange: (sizing: any) => void;
  onColumnOrderChange: OnChangeFn<ColumnOrderState>;
  onColumnVisibilityChange: (visibility: any) => void;
  onHeaderContextMenu: (event: React.MouseEvent, columnDef: any) => void;
  onHeaderDoubleClick: (event: React.MouseEvent, columnDef: any) => void;
  onRowContextMenu: (e: React.MouseEvent, row: any) => void;
  onRowDoubleClick: (row: any) => void;
  onDropOnProductionLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onToggleGroupCollapse: (groupKey: string) => void;
  onCancelGrouping: () => void;

  // Styling
  getColumnBackgroundProps: (columnId: string, isHeader: boolean, isFixed: boolean) => { style: React.CSSProperties, className: string };

  className?: string;
}

export function TaskListTableContent({
  tasks,
  taskGroups,
  tableColumns,
  enableZebraStriping,
  densityStyles,
  groupConfig,
  tableTotalWidth,
  estimateRowHeight,
  columnSizing,
  columnVisibility,
  columnOrder,
  onColumnSizingChange,
  onColumnOrderChange,
  onColumnVisibilityChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  onDropOnProductionLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  getColumnBackgroundProps,
  className
}: TaskListTableContentProps) {
  
  if (groupConfig.enabled) {
    return (
      <div className={cn(
        "flex flex-col h-auto min-h-full",
        className
      )}>
        <div className={cn(
          "flex-1 custom-scrollbar overflow-visible h-auto grouped-mode-container"
        )}>
          <div className="space-y-0 min-h-full">
            {taskGroups.map((group) => (
              <div key={`group-${group.key}-${group.tasks.length}`}>
                <TaskGroupHeader
                  group={group}
                  groupConfig={groupConfig}
                  onToggleCollapse={onToggleGroupCollapse}
                  onCancelGrouping={onCancelGrouping}
                />
                {!group.collapsed && group.tasks.length > 0 && (
                  <div 
                    className="relative h-auto min-h-0" 
                    key={`table-${group.key}`} 
                    style={{ minHeight: 'fit-content' }}
                  >
                    <OptimizedGroupTable
                      tasks={group.tasks}
                      columns={tableColumns}
                      densityStyles={densityStyles}
                      enableZebraStriping={enableZebraStriping}
                      estimateRowHeight={estimateRowHeight}
                      totalTableWidth={tableTotalWidth}
                      columnSizing={columnSizing}
                      onColumnSizingChange={onColumnSizingChange}
                      columnVisibility={columnVisibility}
                      onColumnVisibilityChange={onColumnVisibilityChange}
                      columnOrder={columnOrder}
                      onColumnOrderChange={onColumnOrderChange}
                      onHeaderContextMenu={onHeaderContextMenu}
                      onHeaderDoubleClick={onHeaderDoubleClick}
                      onRowContextMenu={onRowContextMenu}
                      onRowDoubleClick={onRowDoubleClick}
                      getColumnBackgroundProps={(columnId) => getColumnBackgroundProps(columnId, false, false)}
                      onDropOnProductionLine={(taskId, lineIndex) => {
                        // Note: OptimizedGroupTable expects different signature than parent
                        console.log('Drop on production line:', taskId, lineIndex);
                      }}
                      isGroupedMode={true}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Regular table view (non-grouped)
  return (
    <div className={cn(
      "flex flex-col h-full",
      className
    )}>
      <div className="flex-1 custom-scrollbar overflow-auto">
        <VirtualizedTable
          data={tasks}
          columns={tableColumns}
          getRowId={(row) => row.id}
          densityStyles={densityStyles}
          enableZebraStriping={enableZebraStriping}
          estimateRowHeight={estimateRowHeight}
          totalTableWidth={tableTotalWidth + (window?.innerWidth ? window.innerWidth * 0.05 : 0)}
          columnSizing={columnSizing}
          onColumnSizingChange={onColumnSizingChange}
          columnVisibility={columnVisibility}
          onColumnVisibilityChange={onColumnVisibilityChange}
          columnOrder={columnOrder}
          onColumnOrderChange={onColumnOrderChange}
          onHeaderContextMenu={onHeaderContextMenu}
          onHeaderDoubleClick={onHeaderDoubleClick}
          onRowContextMenu={onRowContextMenu}
          onRowDoubleClick={onRowDoubleClick}
          getColumnBackgroundProps={getColumnBackgroundProps}
          onDropOnProductionLine={onDropOnProductionLine}
        />
      </div>
    </div>
  );
}
