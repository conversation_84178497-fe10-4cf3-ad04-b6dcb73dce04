// src/components/sections/task-list/components/task-list-modal-manager.tsx

import React from 'react';
import type { 
  Task, 
  Vehicle, 
  CustomColumnDefinition, 
  ColumnTextStyle, 
  StyleableColumnId,
  InTaskVehicleCardStyle,
  TaskListStoredSettings,
  TaskGroupConfig
} from '@/types';
import { TaskCardConfig } from '@/types/taskCardConfig';
import { TaskListModals } from '../task-list-modals';
import { TaskCardConfigModal } from '../cards/TaskCardConfigModal';
import { TaskGroupConfigModal } from '../modals/task-group-config-modal';
import { TaskListContextMenus } from '../task-list-context-menus';

interface TaskListModalManagerProps {
  // Tanker Note Modal
  isTankerNoteModalOpen: boolean;
  closeTankerNoteModal: () => void;
  selectedTaskForTankerNote: Task | null;

  // Column Visibility Modal
  isColumnVisibilityModalOpen: boolean;
  closeColumnVisibilityModal: () => void;
  allColumns: CustomColumnDefinition[];
  columnVisibility: Record<string, boolean>;
  handleColumnVisibilityChange: (columnId: string, checked: boolean) => void;
  currentOrder: string[];
  handleColumnOrderChange: (newOrder: string[]) => void;

  // Column Specific Style Modal
  isColumnSpecificStyleModalOpen: boolean;
  closeColumnSpecificStyleModal: () => void;
  editingColumnDef: CustomColumnDefinition | null;
  columnTextStyles: Record<StyleableColumnId, ColumnTextStyle | undefined>;
  columnBackgrounds: Record<string, string>;
  handleColumnTextStyleChange: (columnId: StyleableColumnId, property: keyof ColumnTextStyle, value: string) => void;
  handleColumnBackgroundChange: (columnId: string, value: string) => void;

  // Vehicle Card Styler Modal
  isStyleEditorModalOpen: boolean;
  closeStyleEditorModal: () => void;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  updateSetting: <K extends keyof TaskListStoredSettings>(key: K, value: TaskListStoredSettings[K]) => void;
  onVehiclesPerRowChange?: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;

  // Delivery Order Details Modal
  isDeliveryOrderDetailsModalOpen: boolean;
  closeDeliveryOrderDetailsModal: () => void;
  selectedVehicleForDeliveryOrder: Vehicle | null;
  selectedTaskForDeliveryOrder: Task | null;
  
  // Task Reminder Config Modal
  isReminderConfigModalOpen: boolean;
  closeReminderConfigModal: () => void;
  selectedTaskForReminderConfig: Task | null;

  // QR Code Modal
  isQRCodeModalOpen: boolean;
  closeQRCodeModal: () => void;
  selectedTaskForQRCode: Task | null;

  // Task Abbreviation Modal
  isTaskAbbreviationModalOpen: boolean;
  closeTaskAbbreviationModal: () => void;
  selectedTaskForAbbreviation: Task | null;
  handleSaveAbbreviation: (taskId: string, abbreviation: string) => Promise<void>;

  // Task Card Config Modal
  taskCardConfigModalOpen: boolean;
  setTaskCardConfigModalOpen: (open: boolean) => void;
  taskCardConfig: TaskCardConfig;
  handleTaskCardConfigChange: (config: TaskCardConfig) => void;

  // Group Config Modal
  isGroupConfigModalOpen: boolean;
  setIsGroupConfigModalOpen: (open: boolean) => void;
  groupConfig: TaskGroupConfig;
  handleOpenGroupConfig: (config: TaskGroupConfig) => void;

  // Context Menus
  isTaskContextMenuOpen: boolean;
  taskContextMenuPosition: { x: number; y: number } | null;
  contextMenuTaskData: { taskId: string } | null;
  closeTaskContextMenu: () => void;
  openTankerNoteModal: (task: Task) => void;
  openReminderConfigModal: (task: Task) => void;
  openQRCodeModal: (task: Task) => void;
  openTaskAbbreviationModal: (task: Task) => void;
  filteredTasks: Task[];
  isVehicleCardContextMenuOpen: boolean;
  vehicleCardContextMenuPosition: { x: number; y: number } | null;
  vehicleCardContextMenuContext: { vehicle: Vehicle; task: Task } | null;
  closeVehicleCardContextMenu: () => void;
}

export function TaskListModalManager({
  // Tanker Note Modal
  isTankerNoteModalOpen,
  closeTankerNoteModal,
  selectedTaskForTankerNote,

  // Column Visibility Modal
  isColumnVisibilityModalOpen,
  closeColumnVisibilityModal,
  allColumns,
  columnVisibility,
  handleColumnVisibilityChange,
  currentOrder,
  handleColumnOrderChange,

  // Column Specific Style Modal
  isColumnSpecificStyleModalOpen,
  closeColumnSpecificStyleModal,
  editingColumnDef,
  columnTextStyles,
  columnBackgrounds,
  handleColumnTextStyleChange,
  handleColumnBackgroundChange,

  // Vehicle Card Styler Modal
  isStyleEditorModalOpen,
  closeStyleEditorModal,
  inTaskVehicleCardStyles,
  updateSetting,
  onVehiclesPerRowChange,

  // Delivery Order Details Modal
  isDeliveryOrderDetailsModalOpen,
  closeDeliveryOrderDetailsModal,
  selectedVehicleForDeliveryOrder,
  selectedTaskForDeliveryOrder,

  // Task Reminder Config Modal
  isReminderConfigModalOpen,
  closeReminderConfigModal,
  selectedTaskForReminderConfig,

  // QR Code Modal
  isQRCodeModalOpen,
  closeQRCodeModal,
  selectedTaskForQRCode,

  // Task Abbreviation Modal
  isTaskAbbreviationModalOpen,
  closeTaskAbbreviationModal,
  selectedTaskForAbbreviation,
  handleSaveAbbreviation,

  // Task Card Config Modal
  taskCardConfigModalOpen,
  setTaskCardConfigModalOpen,
  taskCardConfig,
  handleTaskCardConfigChange,

  // Group Config Modal
  isGroupConfigModalOpen,
  setIsGroupConfigModalOpen,
  groupConfig,
  handleOpenGroupConfig,

  // Context Menus
  isTaskContextMenuOpen,
  taskContextMenuPosition,
  contextMenuTaskData,
  closeTaskContextMenu,
  openTankerNoteModal,
  openReminderConfigModal,
  openQRCodeModal,
  openTaskAbbreviationModal,
  filteredTasks,
  isVehicleCardContextMenuOpen,
  vehicleCardContextMenuPosition,
  vehicleCardContextMenuContext,
  closeVehicleCardContextMenu,
}: TaskListModalManagerProps) {
  return (
    <>
      {/* Main Task List Modals */}
      <TaskListModals
        isTankerNoteModalOpen={isTankerNoteModalOpen}
        closeTankerNoteModal={closeTankerNoteModal}
        selectedTaskForTankerNote={selectedTaskForTankerNote}
        isColumnVisibilityModalOpen={isColumnVisibilityModalOpen}
        closeColumnVisibilityModal={closeColumnVisibilityModal}
        allColumns={allColumns}
        columnVisibility={columnVisibility}
        handleColumnVisibilityChange={handleColumnVisibilityChange}
        currentOrder={currentOrder}
        handleColumnOrderChange={handleColumnOrderChange}
        isColumnSpecificStyleModalOpen={isColumnSpecificStyleModalOpen}
        closeColumnSpecificStyleModal={closeColumnSpecificStyleModal}
        editingColumnDef={editingColumnDef}
        columnTextStyles={columnTextStyles}
        columnBackgrounds={columnBackgrounds}
        handleColumnTextStyleChange={handleColumnTextStyleChange}
        handleColumnBackgroundChange={handleColumnBackgroundChange}
        isStyleEditorModalOpen={isStyleEditorModalOpen}
        closeStyleEditorModal={closeStyleEditorModal}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        updateSetting={updateSetting}
        isDeliveryOrderDetailsModalOpen={isDeliveryOrderDetailsModalOpen}
        closeDeliveryOrderDetailsModal={closeDeliveryOrderDetailsModal}
        selectedVehicleForDeliveryOrder={selectedVehicleForDeliveryOrder}
        selectedTaskForDeliveryOrder={selectedTaskForDeliveryOrder}
        isReminderConfigModalOpen={isReminderConfigModalOpen}
        selectedTaskForReminderConfig={selectedTaskForReminderConfig}
        closeReminderConfigModal={closeReminderConfigModal}
        isQRCodeModalOpen={isQRCodeModalOpen}
        selectedTaskForQRCode={selectedTaskForQRCode}
        closeQRCodeModal={closeQRCodeModal}
        isTaskAbbreviationModalOpen={isTaskAbbreviationModalOpen}
        selectedTaskForAbbreviation={selectedTaskForAbbreviation}
        closeTaskAbbreviationModal={closeTaskAbbreviationModal}
        handleSaveAbbreviation={handleSaveAbbreviation}
        onVehiclesPerRowChange={onVehiclesPerRowChange}
      />
      
      {/* Task Card Config Modal */}
      <TaskCardConfigModal
        open={taskCardConfigModalOpen}
        onOpenChange={setTaskCardConfigModalOpen}
        config={taskCardConfig}
        onConfigChange={handleTaskCardConfigChange}
      />

      {/* Group Config Modal */}
      <TaskGroupConfigModal
        isOpen={isGroupConfigModalOpen}
        onClose={() => setIsGroupConfigModalOpen(false)}
        groupConfig={groupConfig}
        onUpdateConfig={(config) => {
          // 确保config包含所有必需的TaskGroupConfig属性
          if (config.groupBy !== undefined) {
            handleOpenGroupConfig(config as TaskGroupConfig);
          }
        }}
      />

      {/* Context Menus */}
      <TaskListContextMenus
        isTaskContextMenuOpen={isTaskContextMenuOpen}
        taskContextMenuPosition={taskContextMenuPosition}
        contextMenuTaskData={contextMenuTaskData}
        closeTaskContextMenu={closeTaskContextMenu}
        openTankerNoteModal={openTankerNoteModal}
        openReminderConfigModal={openReminderConfigModal}
        filteredTasks={filteredTasks}
        isVehicleCardContextMenuOpen={isVehicleCardContextMenuOpen}
        vehicleCardContextMenuPosition={vehicleCardContextMenuPosition}
        vehicleCardContextMenuContext={vehicleCardContextMenuContext}
        closeVehicleCardContextMenu={closeVehicleCardContextMenu}
        openDeliveryOrderDetailsModal={function (vehicle: Vehicle, task: Task): void {
          throw new Error('Function not implemented.');
        } }
        cancelVehicleDispatch={function (vehicleId: string): void {
          throw new Error('Function not implemented.');
        } }
        // 第一组：任务时间和状态管理
        onOpenTimeSettingsModal={function (task: Task): void {
          console.log('打开时间设置模态框:', task);
        }}
        onPauseTask={function (task: Task): void {
          console.log('暂停任务:', task);
        }}
        onCompleteTask={function (task: Task): void {
          console.log('完成任务:', task);
        }}
        // 第二组：统计信息查看
        onOpenDispatchDetailsModal={function (task: Task): void {
          console.log('打开调度详情模态框:', task);
        }}
        onOpenTimeStatsModal={function (task: Task): void {
          console.log('打开时间统计模态框:', task);
        }}
        // 第三组：任务详情管理
        onOpenTaskDetailModal={function (task: Task): void {
          console.log('打开任务详情模态框:', task);
        }}
        onOpenTaskProgressModal={function (task: Task): void {
          console.log('打开任务进度模态框:', task);
        }}
        // 第四组：配比管理
        onOpenRatioDisplayModal={function (task: Task): void {
          console.log('打开配比显示模态框:', task);
        }}
        onOpenRatioEditModal={function (task: Task): void {
          console.log('打开配比编辑模态框:', task);
        }}
        // 第五组：界面显示设置
        onOpenColumnSettingsModal={function (): void {
          console.log('打开列设置模态框');
        }}
        onToggleDisplayMode={function (): void {
          console.log('切换显示模式');
        }}
        onResetToDefaultStyle={function (): void {
          console.log('重置为默认样式');
        }}
        // 第六组：扩展功能
        onOpenTaskAbbreviationModal={openTaskAbbreviationModal}
        onPublishWeChatMessage={function (task: Task): void {
          console.log('发布微信消息:', task);
        }}
        onPrintQRCode={openQRCodeModal}
        />
    </>
  );
}
