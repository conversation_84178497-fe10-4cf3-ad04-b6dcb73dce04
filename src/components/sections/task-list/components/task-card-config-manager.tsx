// src/components/sections/task-list/components/task-card-config-manager.tsx

import { useState, useEffect, useCallback } from 'react';
import { flushSync } from 'react-dom';
import { TaskCardConfig, defaultTaskCardConfig } from '@/types/taskCardConfig';
import { useToast } from '@/hooks/use-toast';
import { safeJsonParse } from '../task-list-type-guards';

interface UseTaskCardConfigManagerReturn {
  taskCardConfig: TaskCardConfig;
  isCardConfigLoaded: boolean;
  handleTaskCardConfigChange: (config: TaskCardConfig) => void;
  handleResetTaskCardConfig: () => void;
  handleExportTaskCardConfig: () => void;
  handleImportTaskCardConfig: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

/**
 * 任务卡片配置管理Hook
 * 处理任务卡片配置的加载、保存、导入、导出等功能
 */
export function useTaskCardConfigManager(): UseTaskCardConfigManagerReturn {
  const { toast } = useToast();
  const [taskCardConfig, setTaskCardConfig] = useState<TaskCardConfig>(defaultTaskCardConfig);
  const [isCardConfigLoaded, setIsCardConfigLoaded] = useState(false);

  // 任务卡片配置的localStorage键
  const TASK_CARD_CONFIG_KEY = 'taskCardConfig_v1.0';

  // 加载任务卡片配置
  useEffect(() => {
    try {
      const savedConfig = localStorage.getItem(TASK_CARD_CONFIG_KEY);
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        // 验证配置结构的完整性
        if (parsedConfig && parsedConfig.style && parsedConfig.areas) {
          setTaskCardConfig(parsedConfig);
        } else {
          // 配置结构不完整，使用默认配置
          setTaskCardConfig(defaultTaskCardConfig);
        }
      } else {
        setTaskCardConfig(defaultTaskCardConfig);
      }
    } catch (error) {
      console.warn('Failed to load task card config from localStorage:', error);
      setTaskCardConfig(defaultTaskCardConfig);
    } finally {
      setIsCardConfigLoaded(true);
    }
  }, []);

  const handleTaskCardConfigChange = useCallback((config: TaskCardConfig) => {
    // 使用 flushSync 确保状态立即更新，避免需要刷新页面
    flushSync(() => {
      setTaskCardConfig(config);
    });

    // 保存到localStorage
    try {
      localStorage.setItem(TASK_CARD_CONFIG_KEY, JSON.stringify(config));
      toast({
        title: "配置已保存",
        description: "任务卡片样式配置已成功保存",
      });
    } catch (error) {
      console.error('Failed to save task card config to localStorage:', error);
      toast({
        title: "保存失败",
        description: "任务卡片样式配置保存失败，请重试",
        variant: "destructive",
      });
    }
  }, [toast]);

  // 重置任务卡片配置
  const handleResetTaskCardConfig = useCallback(() => {
    // 使用 flushSync 确保状态立即更新
    flushSync(() => {
      setTaskCardConfig(defaultTaskCardConfig);
    });
    try {
      localStorage.setItem(TASK_CARD_CONFIG_KEY, JSON.stringify(defaultTaskCardConfig));
      toast({
        title: "配置已重置",
        description: "任务卡片样式配置已重置为默认设置",
      });
    } catch (error) {
      console.error('Failed to reset task card config:', error);
      toast({
        title: "重置失败",
        description: "任务卡片样式配置重置失败，请重试",
        variant: "destructive",
      });
    }
  }, [toast]);

  // 导出任务卡片配置
  const handleExportTaskCardConfig = useCallback(() => {
    try {
      const configJson = JSON.stringify(taskCardConfig, null, 2);
      const blob = new Blob([configJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `task-card-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "配置已导出",
        description: "任务卡片样式配置已成功导出",
      });
    } catch (error) {
      console.error('Failed to export task card config:', error);
      toast({
        title: "导出失败",
        description: "任务卡片样式配置导出失败，请重试",
        variant: "destructive",
      });
    }
  }, [taskCardConfig, toast]);

  // 导入任务卡片配置
  const handleImportTaskCardConfig = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const configJson = e.target?.result;
        if (typeof configJson !== 'string') {
          throw new Error('Invalid file content');
        }
        const importedConfig = safeJsonParse(configJson);
        if (!importedConfig) {
          throw new Error('Invalid JSON format');
        }

        // 验证配置结构
        if (importedConfig && importedConfig.style && importedConfig.areas) {
          // 使用 flushSync 确保状态立即更新
          flushSync(() => {
            setTaskCardConfig(importedConfig);
          });
          localStorage.setItem(TASK_CARD_CONFIG_KEY, JSON.stringify(importedConfig));

          toast({
            title: "配置已导入",
            description: "任务卡片样式配置已成功导入并应用",
          });
        } else {
          throw new Error('Invalid config structure');
        }
      } catch (error) {
        console.error('Failed to import task card config:', error);
        toast({
          title: "导入失败",
          description: "任务卡片样式配置文件格式不正确，请检查文件",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);

    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  }, [toast]);

  return {
    taskCardConfig,
    isCardConfigLoaded,
    handleTaskCardConfigChange,
    handleResetTaskCardConfig,
    handleExportTaskCardConfig,
    handleImportTaskCardConfig,
  };
}
