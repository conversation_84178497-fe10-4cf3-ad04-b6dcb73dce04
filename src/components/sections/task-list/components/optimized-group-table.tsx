import React, { memo } from 'react';
import { VirtualizedTable } from '@/components/ui/virtualized-table';
import { Task } from '@/types';
import { ColumnDef, OnChangeFn, ColumnOrderState } from '@tanstack/react-table';
import type { DensityStyleValues } from '@/types';

/**
 * 优化的分组表格组件
 * 使用React.memo减少不必要的重新渲染
 */
interface OptimizedGroupTableProps {
  tasks: Task[];
  columns: ColumnDef<Task>[];
  densityStyles: DensityStyleValues;
  enableZebraStriping: boolean;
  estimateRowHeight: (task?: Task) => number;
  totalTableWidth: number;
  columnSizing: Record<string, number>;
  onColumnSizingChange: (updater: any) => void;
  columnVisibility: Record<string, boolean>;
  onColumnVisibilityChange: (updater: any) => void;
  columnOrder: string[];
  onColumnOrderChange: OnChangeFn<ColumnOrderState>;
  onHeaderContextMenu?: (event: React.MouseEvent, columnDef: any) => void;
  onHeaderDoubleClick?: (event: React.MouseEvent, columnDef: any) => void;
  onRowContextMenu?: (event: React.MouseEvent, row: any) => void;
  onRowDoubleClick?: (row: any) => void;
  getColumnBackgroundProps?: (columnId: string) => any;
  onDropOnProductionLine?: (taskId: string, lineIndex: number) => void;
  isGroupedMode?: boolean; // 新增：是否为分组模式
}

/**
 * 优化的分组表格组件
 * 通过memo和稳定的props减少重新渲染
 */
/**
 * 优化的分组表格组件
 * 通过memo和稳定的props减少重新渲染
 */
export const OptimizedGroupTable = memo(({
  tasks,
  columns,
  densityStyles,
  enableZebraStriping,
  estimateRowHeight,
  totalTableWidth,
  columnSizing,
  onColumnSizingChange,
  columnVisibility,
  onColumnVisibilityChange,
  columnOrder,
  onColumnOrderChange,
  onHeaderContextMenu,
  onHeaderDoubleClick,
  onRowContextMenu,
  onRowDoubleClick,
  getColumnBackgroundProps,
  onDropOnProductionLine,
  isGroupedMode = false,
}: OptimizedGroupTableProps) => {
  return (
    <VirtualizedTable
      data={tasks}
      columns={columns}
      getRowId={(row) => row.id}
      densityStyles={densityStyles}
      enableZebraStriping={enableZebraStriping}
      estimateRowHeight={(task?: Task) => task ? estimateRowHeight(task) : 0}
      totalTableWidth={totalTableWidth}
      columnSizing={columnSizing}
      onColumnSizingChange={onColumnSizingChange}
      columnVisibility={columnVisibility}
      onColumnVisibilityChange={onColumnVisibilityChange}
      columnOrder={columnOrder}
      onColumnOrderChange={onColumnOrderChange}
      onHeaderContextMenu={onHeaderContextMenu}
      onHeaderDoubleClick={onHeaderDoubleClick}
      onRowContextMenu={onRowContextMenu}
      onRowDoubleClick={onRowDoubleClick}
      getColumnBackgroundProps={getColumnBackgroundProps}
      onDropOnProductionLine={(vehicle, taskId, lineId) => 
        onDropOnProductionLine?.(taskId, parseInt(lineId))}
      isGroupedMode={isGroupedMode}
    />
  );
}, (
  prevProps: OptimizedGroupTableProps,
  nextProps: OptimizedGroupTableProps
) => {
  // 自定义比较函数，只在关键属性变化时重新渲染
  return (
    prevProps.tasks === nextProps.tasks &&
    prevProps.columns === nextProps.columns &&
    prevProps.densityStyles === nextProps.densityStyles &&
    prevProps.enableZebraStriping === nextProps.enableZebraStriping &&
    prevProps.totalTableWidth === nextProps.totalTableWidth &&
    prevProps.columnSizing === nextProps.columnSizing &&
    prevProps.columnVisibility === nextProps.columnVisibility &&
    prevProps.columnOrder === nextProps.columnOrder
  );
});
