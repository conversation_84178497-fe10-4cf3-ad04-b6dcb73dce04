'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import type { Task } from '@/types';

interface TaskStatusBadgeProps {
  status: Task['dispatchStatus'];
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  variant?: 'default' | 'outline' | 'solid';
}

export const TaskStatusBadge: React.FC<TaskStatusBadgeProps> = React.memo(({
  status,
  size = 'medium',
  variant = 'default',
}) => {
  // 获取状态配置
  const getStatusConfig = () => {
    switch (status) {
      case 'New':
        return {
          label: '新建',
          className: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800',
          dotColor: 'bg-gray-500',
        };
      case 'ReadyToProduce':
        return {
          label: '待生产',
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
          dotColor: 'bg-yellow-500',
        };
      case 'RatioSet':
        return {
          label: '配比设定',
          className: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',
          dotColor: 'bg-orange-500',
        };
      case 'Paused':
        return {
          label: '暂停',
          className: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800',
          dotColor: 'bg-purple-500',
        };
      case 'InProgress':
        return {
          label: '进行中',
          className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
          dotColor: 'bg-blue-500',
        };
      case 'Completed':
        return {
          label: '已完成',
          className: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
          dotColor: 'bg-green-500',
        };
      case 'Cancelled':
        return {
          label: '已取消',
          className: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
          dotColor: 'bg-red-500',
        };
      default:
        return {
          label: '未知',
          className: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800',
          dotColor: 'bg-gray-500',
        };
    }
  };

  // 获取尺寸样式
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          badge: 'px-2 py-0.5 text-xs',
          dot: 'w-1.5 h-1.5',
          gap: 'gap-1',
        };
      case 'large':
        return {
          badge: 'px-4 py-2 text-base',
          dot: 'w-3 h-3',
          gap: 'gap-2',
        };
      case 'extra-large':
        return {
          badge: 'px-5 py-2.5 text-lg',
          dot: 'w-3.5 h-3.5',
          gap: 'gap-2.5',
        };
      default: // medium
        return {
          badge: 'px-3 py-1 text-sm',
          dot: 'w-2 h-2',
          gap: 'gap-1.5',
        };
    }
  };

  const statusConfig = getStatusConfig();
  const sizeStyles = getSizeStyles();

  if (variant === 'outline') {
    return (
      <Badge
        variant="outline"
        className={cn(
          "font-medium border transition-all duration-200",
          sizeStyles.badge,
          statusConfig.className
        )}
      >
        <div className={cn("flex items-center", sizeStyles.gap)}>
          <div className={cn(
            "rounded-full flex-shrink-0",
            sizeStyles.dot,
            statusConfig.dotColor
          )} />
          {statusConfig.label}
        </div>
      </Badge>
    );
  }

  if (variant === 'solid') {
    return (
      <div className={cn(
        "inline-flex items-center font-medium rounded-full transition-all duration-200",
        sizeStyles.badge,
        sizeStyles.gap,
        statusConfig.className
      )}>
        <div className={cn(
          "rounded-full flex-shrink-0 bg-current opacity-70",
          sizeStyles.dot
        )} />
        {statusConfig.label}
      </div>
    );
  }

  // Default variant
  return (
    <Badge
      className={cn(
        "font-medium transition-all duration-200 hover:scale-105",
        sizeStyles.badge,
        statusConfig.className
      )}
    >
      <div className={cn("flex items-center", sizeStyles.gap)}>
        <div className={cn(
          "rounded-full flex-shrink-0 animate-pulse",
          sizeStyles.dot,
          statusConfig.dotColor
        )} />
        {statusConfig.label}
      </div>
    </Badge>
  );
});

TaskStatusBadge.displayName = 'TaskStatusBadge';
