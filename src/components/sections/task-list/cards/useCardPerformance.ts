import { useEffect, useMemo, useCallback, useRef } from 'react';
import { 
  PerformanceConfig, 
  getOptimalPerformanceConfig,
  PerformanceMonitor,
  optimizeScrollPerformance,
  optimizeAnimationPerformance
} from './CardPerformanceConfig';

interface UseCardPerformanceOptions {
  componentName: string;
  config?: Partial<PerformanceConfig>;
  enableMonitoring?: boolean;
}

export const useCardPerformance = ({
  componentName,
  config: userConfig,
  enableMonitoring = false,
}: UseCardPerformanceOptions) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const renderCountRef = useRef(0);
  const lastRenderTimeRef = useRef(0);
  
  // 获取优化的性能配置
  const performanceConfig = useMemo(() => {
    const optimalConfig = getOptimalPerformanceConfig();
    return userConfig ? { ...optimalConfig, ...userConfig } : optimalConfig;
  }, [userConfig]);
  
  // 性能监控器
  const monitor = useMemo(() => {
    return enableMonitoring ? PerformanceMonitor.getInstance() : null;
  }, [enableMonitoring]);
  
  // 测量渲染性能
  const measureRender = useCallback((renderFn: () => void) => {
    if (!monitor) {
      renderFn();
      return;
    }
    
    const start = performance.now();
    renderFn();
    const end = performance.now();
    
    const duration = end - start;
    monitor.recordRenderTime(componentName, duration);
    
    renderCountRef.current++;
    lastRenderTimeRef.current = duration;
    
    // 如果渲染时间过长，发出警告
    if (duration > 16) { // 超过一帧的时间
      console.warn(`[Performance] ${componentName} render took ${duration.toFixed(2)}ms`);
    }
  }, [monitor, componentName]);
  
  // 防抖的配置更新
  const debouncedConfigUpdate = useCallback(
    debounce((newConfig: Partial<PerformanceConfig>) => {
      // 更新配置逻辑
    }, performanceConfig.rendering.debounceMs),
    [performanceConfig.rendering.debounceMs]
  );
  
  // 优化容器元素
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    // 应用滚动优化
    optimizeScrollPerformance(container, performanceConfig);
    
    // 应用动画优化
    optimizeAnimationPerformance(container, performanceConfig);
    
    // 设置 CSS 变量
    const cssVars = {
      '--animation-duration': `${performanceConfig.animations.duration}ms`,
      '--animation-easing': performanceConfig.animations.easing,
      '--scroll-behavior': performanceConfig.scrolling.scrollBehavior,
    };
    
    Object.entries(cssVars).forEach(([key, value]) => {
      container.style.setProperty(key, value);
    });
    
    // 清理函数
    return () => {
      container.style.willChange = 'auto';
    };
  }, [performanceConfig]);
  
  // 监听用户偏好变化
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      if (e.matches && performanceConfig.animations.enabled) {
        // 用户偏好减少动画，自动调整配置
        debouncedConfigUpdate({
          animations: {
            ...performanceConfig.animations,
            enabled: false,
            reduceMotion: true,
          },
        });
      }
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [performanceConfig.animations, debouncedConfigUpdate]);
  
  // 内存使用监控
  useEffect(() => {
    if (!enableMonitoring) return;
    
    const checkMemoryUsage = () => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        const usedMB = memInfo.usedJSHeapSize / 1024 / 1024;
        const totalMB = memInfo.totalJSHeapSize / 1024 / 1024;
        
        // 如果内存使用超过阈值，发出警告
        if (usedMB / totalMB > 0.8) {
          console.warn(`[Performance] High memory usage: ${usedMB.toFixed(2)}MB / ${totalMB.toFixed(2)}MB`);
        }
      }
    };
    
    const interval = setInterval(checkMemoryUsage, 10000); // 每10秒检查一次
    return () => clearInterval(interval);
  }, [enableMonitoring]);
  
  // 获取性能统计
  const getPerformanceStats = useCallback(() => {
    if (!monitor) return null;
    
    return {
      averageRenderTime: monitor.getAverageRenderTime(componentName),
      renderCount: renderCountRef.current,
      lastRenderTime: lastRenderTimeRef.current,
      fullReport: monitor.getPerformanceReport(),
    };
  }, [monitor, componentName]);
  
  // 重置性能统计
  const resetPerformanceStats = useCallback(() => {
    if (monitor) {
      monitor.clearMetrics();
    }
    renderCountRef.current = 0;
    lastRenderTimeRef.current = 0;
  }, [monitor]);
  
  // 检查是否应该使用虚拟滚动 - 降低阈值以更早启用
  const shouldUseVirtualScroll = useCallback((itemCount: number) => {
    return performanceConfig.virtualScroll.enabled && itemCount > 20;
  }, [performanceConfig.virtualScroll.enabled]);
  
  // 获取虚拟滚动配置
  const getVirtualScrollConfig = useCallback(() => {
    return {
      overscan: performanceConfig.virtualScroll.overscan,
      increaseViewportBy: performanceConfig.virtualScroll.increaseViewportBy,
      itemHeight: performanceConfig.virtualScroll.itemHeight,
    };
  }, [performanceConfig.virtualScroll]);
  
  // 动态调整性能配置
  const adjustPerformanceConfig = useCallback((itemCount: number, isScrolling: boolean) => {
    let adjustedConfig = { ...performanceConfig };
    
    // 根据项目数量调整
    if (itemCount > 100) {
      adjustedConfig.animations.enabled = false;
      adjustedConfig.virtualScroll.overscan = 1;
    }
    
    // 滚动时减少动画
    if (isScrolling) {
      adjustedConfig.animations.enabled = false;
    }
    
    return adjustedConfig;
  }, [performanceConfig]);
  
  return {
    containerRef,
    performanceConfig,
    measureRender,
    getPerformanceStats,
    resetPerformanceStats,
    shouldUseVirtualScroll,
    getVirtualScrollConfig,
    adjustPerformanceConfig,
  };
};

// 防抖工具函数
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流工具函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastTime >= wait) {
      lastTime = now;
      func(...args);
    }
  };
}

// 性能优化的样式计算 Hook
export const useOptimizedStyles = <T>(
  computeStyles: () => T,
  dependencies: React.DependencyList
): T => {
  return useMemo(computeStyles, dependencies);
};

// 性能优化的事件处理 Hook
export const useOptimizedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  dependencies: React.DependencyList
): T => {
  return useCallback(callback, dependencies);
};
