'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { PerformanceMonitor } from './CardPerformanceConfig';
import { 
  Activity, 
  Clock, 
  Cpu, 
  MemoryStick, 
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle2
} from 'lucide-react';

interface PerformanceMonitorPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

export const PerformanceMonitorPanel: React.FC<PerformanceMonitorPanelProps> = ({
  isVisible,
  onClose,
}) => {
  const [performanceData, setPerformanceData] = useState<any>(null);
  const [memoryInfo, setMemoryInfo] = useState<any>(null);
  const [frameRate, setFrameRate] = useState<number>(0);
  
  const monitor = PerformanceMonitor.getInstance();

  // 更新性能数据
  useEffect(() => {
    if (!isVisible) return;

    const updatePerformanceData = () => {
      const report = monitor.getPerformanceReport();
      setPerformanceData(report);

      // 获取内存信息
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        setMemoryInfo({
          used: Math.round(memInfo.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memInfo.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(memInfo.jsHeapSizeLimit / 1024 / 1024),
        });
      }
    };

    // 测量帧率
    let frameCount = 0;
    let lastTime = performance.now();
    
    const measureFrameRate = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setFrameRate(frameCount);
        frameCount = 0;
        lastTime = currentTime;
      }
      
      if (isVisible) {
        requestAnimationFrame(measureFrameRate);
      }
    };

    updatePerformanceData();
    measureFrameRate();
    
    const interval = setInterval(updatePerformanceData, 1000);
    
    return () => {
      clearInterval(interval);
    };
  }, [isVisible, monitor]);

  if (!isVisible) return null;

  // 获取性能等级
  const getPerformanceLevel = (avgTime: number) => {
    if (avgTime < 5) return { level: 'excellent', color: 'green', icon: CheckCircle2 };
    if (avgTime < 10) return { level: 'good', color: 'blue', icon: CheckCircle2 };
    if (avgTime < 16) return { level: 'fair', color: 'yellow', icon: AlertTriangle };
    return { level: 'poor', color: 'red', icon: AlertTriangle };
  };

  // 获取内存使用等级
  const getMemoryLevel = () => {
    if (!memoryInfo) return { level: 'unknown', color: 'gray', percentage: 0 };
    
    const percentage = (memoryInfo.used / memoryInfo.total) * 100;
    
    if (percentage < 50) return { level: 'low', color: 'green', percentage };
    if (percentage < 75) return { level: 'medium', color: 'yellow', percentage };
    return { level: 'high', color: 'red', percentage };
  };

  const memoryLevel = getMemoryLevel();

  return (
    <div className="fixed top-4 right-4 z-50 w-80 max-h-[80vh] overflow-y-auto">
      <Card className="bg-background/95 backdrop-blur border shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Activity className="w-4 h-4" />
              性能监控
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 帧率监控 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium flex items-center gap-2">
                <Zap className="w-3 h-3" />
                帧率
              </span>
              <Badge variant={frameRate >= 55 ? 'default' : frameRate >= 30 ? 'secondary' : 'destructive'}>
                {frameRate} FPS
              </Badge>
            </div>
            <Progress 
              value={Math.min(frameRate, 60)} 
              max={60}
              className="h-2"
            />
          </div>

          {/* 内存使用 */}
          {memoryInfo && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium flex items-center gap-2">
                  <MemoryStick className="w-3 h-3" />
                  内存使用
                </span>
                <Badge variant={memoryLevel.color === 'green' ? 'default' : 
                              memoryLevel.color === 'yellow' ? 'secondary' : 'destructive'}>
                  {memoryInfo.used}MB / {memoryInfo.total}MB
                </Badge>
              </div>
              <Progress 
                value={memoryLevel.percentage} 
                max={100}
                className="h-2"
              />
              <div className="text-xs text-muted-foreground">
                限制: {memoryInfo.limit}MB
              </div>
            </div>
          )}

          {/* 组件性能 */}
          {performanceData && (
            <div className="space-y-3">
              <div className="text-sm font-medium flex items-center gap-2">
                <Cpu className="w-3 h-3" />
                组件性能
              </div>
              
              {Object.entries(performanceData).map(([componentName, data]: [string, any]) => {
                const perfLevel = getPerformanceLevel(data.avg);
                const Icon = perfLevel.icon;
                
                return (
                  <div key={componentName} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium truncate flex items-center gap-1">
                        <Icon className="w-3 h-3" />
                        {componentName}
                      </span>
                      <Badge 
                        variant={perfLevel.color === 'green' ? 'default' : 
                                perfLevel.color === 'blue' ? 'secondary' : 
                                perfLevel.color === 'yellow' ? 'outline' : 'destructive'}
                        className="text-xs"
                      >
                        {data.avg.toFixed(1)}ms
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="w-2 h-2" />
                        平均: {data.avg.toFixed(1)}ms
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-2 h-2" />
                        最大: {data.max.toFixed(1)}ms
                      </div>
                      <div className="flex items-center gap-1">
                        <Activity className="w-2 h-2" />
                        次数: {data.count}
                      </div>
                    </div>
                    
                    <Progress 
                      value={Math.min(data.avg, 16)} 
                      max={16}
                      className="h-1"
                    />
                  </div>
                );
              })}
            </div>
          )}

          {/* 性能建议 */}
          <div className="space-y-2">
            <div className="text-sm font-medium">性能建议</div>
            <div className="space-y-1 text-xs text-muted-foreground">
              {frameRate < 30 && (
                <div className="flex items-center gap-2 text-orange-600">
                  <AlertTriangle className="w-3 h-3" />
                  帧率较低，建议减少动画效果
                </div>
              )}
              
              {memoryLevel.percentage > 75 && (
                <div className="flex items-center gap-2 text-red-600">
                  <AlertTriangle className="w-3 h-3" />
                  内存使用过高，建议启用虚拟滚动
                </div>
              )}
              
              {performanceData && Object.values(performanceData).some((data: any) => data.avg > 16) && (
                <div className="flex items-center gap-2 text-yellow-600">
                  <AlertTriangle className="w-3 h-3" />
                  组件渲染时间过长，建议优化
                </div>
              )}
              
              {frameRate >= 55 && memoryLevel.percentage < 50 && (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle2 className="w-3 h-3" />
                  性能表现良好
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => monitor.clearMetrics()}
              className="flex-1"
            >
              清除数据
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                const report = monitor.getPerformanceReport();
                console.log('Performance Report:', report);
                console.log('Memory Info:', memoryInfo);
                console.log('Frame Rate:', frameRate);
              }}
              className="flex-1"
            >
              导出报告
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
