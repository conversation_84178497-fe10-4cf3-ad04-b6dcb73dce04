'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import {
  MessageCircle,
  Clock,
  AlertTriangle,
  Info,
  HelpCircle,
  CheckCircle2,
} from 'lucide-react';
import type { Task, TaskMessage } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface TaskMessageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task: Task;
  onMarkAsRead?: (messageId: string) => void;
  onMarkAllAsRead?: (taskId: string) => void;
}

// 获取消息类型图标
const getMessageTypeIcon = (type: TaskMessage['type']) => {
  switch (type) {
    case 'info':
      return Info;
    case 'warning':
      return AlertTriangle;
    case 'urgent':
      return AlertTriangle;
    case 'question':
      return HelpCircle;
    default:
      return MessageCircle;
  }
};

// 获取消息类型样式
const getMessageTypeStyle = (type: TaskMessage['type'], priority: TaskMessage['priority']) => {
  const baseClasses = 'border-l-4 pl-4';
  
  if (priority === 'urgent') {
    return `${baseClasses} border-l-red-500 bg-red-50`;
  }
  
  switch (type) {
    case 'warning':
      return `${baseClasses} border-l-yellow-500 bg-yellow-50`;
    case 'urgent':
      return `${baseClasses} border-l-red-500 bg-red-50`;
    case 'question':
      return `${baseClasses} border-l-blue-500 bg-blue-50`;
    case 'info':
    default:
      return `${baseClasses} border-l-gray-300 bg-gray-50`;
  }
};

// 获取优先级标签样式
const getPriorityBadgeStyle = (priority: TaskMessage['priority']) => {
  switch (priority) {
    case 'urgent':
      return 'bg-red-100 text-red-700 border-red-200';
    case 'high':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'normal':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'low':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

// 获取优先级标签文本
const getPriorityText = (priority: TaskMessage['priority']) => {
  switch (priority) {
    case 'urgent':
      return '紧急';
    case 'high':
      return '重要';
    case 'normal':
      return '普通';
    case 'low':
      return '一般';
    default:
      return '普通';
  }
};

export const TaskMessageModal: React.FC<TaskMessageModalProps> = ({
  open,
  onOpenChange,
  task,
  onMarkAsRead,
  onMarkAllAsRead,
}) => {
  const messages = task.messages || [];
  const unreadCount = messages.filter(msg => !msg.isRead).length;

  const handleMarkAsRead = (messageId: string) => {
    onMarkAsRead?.(messageId);
  };

  const handleMarkAllAsRead = () => {
    onMarkAllAsRead?.(task.id);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5 text-blue-600" />
            <span>任务消息</span>
            <Badge variant="outline" className="ml-2">
              {task.taskNumber}
            </Badge>
            {unreadCount > 0 && (
              <Badge className="bg-red-500 text-white">
                {unreadCount} 条未读
              </Badge>
            )}
          </DialogTitle>
          <div className="text-sm text-muted-foreground">
            {task.projectName} - {task.constructionSite}
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* 操作按钮 */}
          {unreadCount > 0 && (
            <div className="flex justify-end">
              <button
                onClick={handleMarkAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
              >
                <CheckCircle2 className="w-4 h-4" />
                全部标记为已读
              </button>
            </div>
          )}

          {/* 消息列表 */}
          <ScrollArea className="h-[400px] pr-4">
            {messages.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <MessageCircle className="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
                <p>暂无消息</p>
              </div>
            ) : (
              <div className="space-y-3">
                {[...messages] // 创建数组副本
                  .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                  .map((message) => {
                    const Icon = getMessageTypeIcon(message.type);
                    
                    return (
                      <div
                        key={message.id}
                        className={cn(
                          'p-4 rounded-lg transition-all duration-200',
                          getMessageTypeStyle(message.type, message.priority),
                          !message.isRead && 'ring-2 ring-blue-200',
                          'cursor-pointer hover:shadow-md'
                        )}
                        onClick={() => !message.isRead && handleMarkAsRead(message.id)}
                      >
                        <div className="flex items-start gap-3">
                          <Icon className="w-5 h-5 mt-0.5 flex-shrink-0" />
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="font-medium text-sm">
                                {message.sender}
                              </span>
                              <Badge 
                                variant="outline" 
                                className={cn('text-xs', getPriorityBadgeStyle(message.priority))}
                              >
                                {getPriorityText(message.priority)}
                              </Badge>
                              {!message.isRead && (
                                <Badge className="bg-blue-500 text-white text-xs">
                                  未读
                                </Badge>
                              )}
                            </div>
                            
                            <p className="text-sm text-gray-700 mb-2 leading-relaxed">
                              {message.content}
                            </p>
                            
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Clock className="w-3 h-3" />
                              <span>
                                {formatDistanceToNow(new Date(message.timestamp), {
                                  addSuffix: true,
                                  locale: zhCN,
                                })}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            )}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};
