'use client';

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { cn } from '@/lib/utils';
import { useDragDropContext } from '@/contexts/DragDropContext';
import type { Vehicle, Task } from '@/types';

interface DraggableVehicleCardProps {
  vehicle: Vehicle;
  index: number;
  task?: Task;
  productionLineId?: string;
  vehicleDisplayMode?: 'compact' | 'normal' | 'detailed';
  inTaskVehicleCardStyles?: any;
  productionLineCount?: number;
  density?: 'compact' | 'normal' | 'loose';
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string) => void;
  onOpenContextMenu?: (e: React.MouseEvent, vehicle: Vehicle) => void;
  className?: string;
}

export const DraggableVehicleCard: React.FC<DraggableVehicleCardProps> = ({
  vehicle,
  index,
  task,
  productionLineId,
  vehicleDisplayMode = 'normal',
  inTaskVehicleCardStyles,
  productionLineCount = 1,
  density = 'normal',
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenContextMenu,
  className,
}) => {
  const { state } = useDragDropContext();

  // 获取车辆状态相关的样式
  const getVehicleStatusStyles = () => {
    const baseStyles = "transition-all duration-200 ease-in-out";
    
    switch (vehicle.status) {
      case 'pending':
        return cn(baseStyles, "bg-gray-100 border-gray-300 text-gray-700");
      case 'outbound':
        return cn(baseStyles, "bg-blue-100 border-blue-300 text-blue-800");
      case 'outbound':
        return cn(baseStyles, "bg-green-100 border-green-300 text-green-800");
      case 'returned':
        return cn(baseStyles, "bg-purple-100 border-purple-300 text-purple-800");
      default:
        return cn(baseStyles, "bg-gray-100 border-gray-300 text-gray-700");
    }
  };

  // 获取生产状态指示器样式
  const getProductionStatusStyles = () => {
    const currentProductionStatus = vehicle.productionStatus || 'unknown';
    
    switch (currentProductionStatus) {
      case 'queued':
        return "bg-yellow-500";
      case 'producing':
        return "bg-blue-500";
      case 'produced':
        return "bg-green-500";
      case 'weighed':
        return "bg-orange-500";
      case 'ticketed':
        return "bg-purple-500";
      case 'shipped':
        return "bg-gray-500";
      default:
        return "bg-gray-300";
    }
  };

  // 获取车辆标识符
  const getVehicleIdentifier = () => {
    if (vehicleDisplayMode === 'compact') {
      return vehicle.vehicleNumber?.slice(-2) || vehicle.id.slice(-2);
    }
    return vehicle.vehicleNumber || vehicle.id;
  };

  // 获取密度相关的样式
  const getDensityStyles = () => {
    switch (density) {
      case 'compact':
        return {
          container: "h-6 px-1 py-0.5 text-[8px]",
          content: "gap-0.5",
          statusDot: "w-1.5 h-1.5",
          text: "text-[8px]",
        };
      case 'loose':
        return {
          container: "h-12 px-3 py-2 text-sm",
          content: "gap-2",
          statusDot: "w-3 h-3",
          text: "text-sm",
        };
      default:
        return {
          container: "h-8 px-2 py-1 text-xs",
          content: "gap-1",
          statusDot: "w-2 h-2",
          text: "text-xs",
        };
    }
  };

  const densityStyles = getDensityStyles();

  // 处理双击事件
  const handleDoubleClick = () => {
    if (onOpenDeliveryOrderDetails) {
      onOpenDeliveryOrderDetails(vehicle.id);
    }
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    if (onOpenContextMenu) {
      onOpenContextMenu(e, vehicle);
    }
  };

  // 设置拖拽数据
  const dragData = {
    type: task ? 'vehicle-to-task' : 'vehicle-reorder',
    vehicleId: vehicle.id,
    taskId: task?.id,
    productionLineId: productionLineId,
  };

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: vehicle.id,
    data: dragData,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0) ${isDragging ? 'rotate(2deg)' : ''}`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={cn(
        "border rounded shadow-sm flex items-center justify-center cursor-grab active:cursor-grabbing",
        densityStyles.container,
        getVehicleStatusStyles(),
        // 拖拽状态样式
        isDragging && [
          "shadow-2xl ring-2 ring-primary/50 scale-105",
          "bg-white border-primary z-50",
          "transform-gpu opacity-50" // 启用GPU加速
        ],
        // 拖拽目标高亮
        state.isDragging &&
        state.draggedVehicleId !== vehicle.id &&
        state.targetTaskId === task?.id && [
          "ring-2 ring-blue-400/50 bg-blue-50/50",
          "animate-pulse"
        ],
        className
      )}
      onDoubleClick={handleDoubleClick}
      onContextMenu={handleContextMenu}
      title={`车辆: ${vehicle.vehicleNumber || vehicle.id}\n状态: ${vehicle.status}\n生产状态: ${vehicle.productionStatus || '未知'}`}
    >
          <div className={cn("flex items-center", densityStyles.content)}>
            {/* 生产状态指示器 */}
            <div className={cn(
              "rounded-full flex-shrink-0",
              densityStyles.statusDot,
              getProductionStatusStyles()
            )} />
            
            {/* 车辆编号 */}
            <span className={cn(
              "font-medium truncate",
              densityStyles.text
            )}>
              {getVehicleIdentifier()}
            </span>

            {/* 编辑权限指示器 */}
            {vehicle.allowWeighRoomEdit && (
              <div 
                className={cn(
                  "rounded-full bg-yellow-400 flex-shrink-0",
                  densityStyles.statusDot
                )}
                title="允许磅房修改数量"
              />
            )}
          </div>

        {/* 拖拽时的视觉反馈 */}
        {isDragging && (
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded pointer-events-none" />
        )}
      </div>
  );
};
