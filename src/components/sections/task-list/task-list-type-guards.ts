// src/components/sections/task-list/task-list-type-guards.ts

import type {
  StyleableColumnId,
  TaskColumnId,
  TaskListDisplayMode,
  TaskListDensityMode,
  VehicleDisplayMode,
  Task,
  Vehicle,
  CustomColumnDefinition,
  ColumnTextStyle,
  ColumnTextStyles
} from '@/types';

/**
 * Type guard to check if a string is a valid StyleableColumnId
 */
export function isStyleableColumnId(value: string): value is StyleableColumnId {
  const validIds: StyleableColumnId[] = [
    'messages', 'dispatchReminder', 'taskNumber', 'projectName', 'projectAbbreviation',
    'constructionUnit', 'constructionSite', 'strength', 'pouringMethod', 'vehicleCount',
    'completedProgress', 'requiredVolume', 'completedVolume', 'pumpTruck', 'otherRequirements',
    'contactPhone', 'supplyTime', 'supplyDate', 'publishDate', 'dispatchedVehicles', 'productionLines'
  ];
  return validIds.includes(value as StyleableColumnId);
}

/**
 * Type guard to check if a string is a valid TaskColumnId
 */
export function isTaskColumnId(value: string): value is TaskColumnId {
  // TaskColumnId includes all StyleableColumnId values
  return isStyleableColumnId(value);
}

/**
 * Type guard to check if a string is a valid TaskListDisplayMode
 */
export function isTaskListDisplayMode(value: string): value is TaskListDisplayMode {
  return value === 'table' || value === 'card';
}

/**
 * Type guard to check if a string is a valid TaskListDensityMode (excluding 'table' and 'card')
 */
export function isTaskListDensityMode(value: string): value is Exclude<TaskListDensityMode, 'table' | 'card'> {
  return value === 'compact' || value === 'normal' || value === 'loose';
}

/**
 * Type guard to check if a string is a valid VehicleDisplayMode
 */
export function isVehicleDisplayMode(value: string): value is VehicleDisplayMode {
  return value === 'licensePlate' || value === 'internalId';
}

/**
 * Type guard to check if an object has the structure of a Task with vehicles
 */
export function isTaskWithVehicles(obj: any): obj is Task & { vehicles: Vehicle[] } {
  return obj && 
         typeof obj === 'object' && 
         typeof obj.id === 'string' &&
         Array.isArray(obj.vehicles);
}

/**
 * Type guard to check if an object is a valid Task
 */
export function isTask(obj: any): obj is Task {
  return obj && 
         typeof obj === 'object' && 
         typeof obj.id === 'string' &&
         typeof obj.taskNumber === 'string' &&
         typeof obj.projectName === 'string';
}

/**
 * Type guard to check if an object has the structure of a CustomColumnDefinition
 */
export function isCustomColumnDefinition(obj: any): obj is CustomColumnDefinition {
  return obj && 
         typeof obj === 'object' && 
         typeof obj.id === 'string' &&
         typeof obj.label === 'string' &&
         typeof obj.defaultVisible === 'boolean';
}

/**
 * Type guard to check if an object is a valid ColumnTextStyle
 */
export function isColumnTextStyle(obj: any): obj is ColumnTextStyle {
  return obj && 
         typeof obj === 'object' && 
         (obj.color === undefined || typeof obj.color === 'string') &&
         (obj.fontWeight === undefined || typeof obj.fontWeight === 'string') &&
         (obj.fontSize === undefined || typeof obj.fontSize === 'string');
}

/**
 * Safe getter for StyleableColumnId with fallback
 */
export function getStyleableColumnId(value: string, fallback: StyleableColumnId = 'taskNumber'): StyleableColumnId {
  return isStyleableColumnId(value) ? value : fallback;
}

/**
 * Safe getter for TaskColumnId with fallback
 */
export function getTaskColumnId(value: string, fallback: TaskColumnId = 'taskNumber'): TaskColumnId {
  return isTaskColumnId(value) ? value : fallback;
}

/**
 * Safe getter for TaskListDisplayMode with fallback
 */
export function getTaskListDisplayMode(value: string, fallback: TaskListDisplayMode = 'table'): TaskListDisplayMode {
  return isTaskListDisplayMode(value) ? value : fallback;
}

/**
 * Safe getter for TaskListDensityMode with fallback
 */
export function getTaskListDensityMode(value: string, fallback: Exclude<TaskListDensityMode, 'table' | 'card'> = 'compact'): Exclude<TaskListDensityMode, 'table' | 'card'> {
  return isTaskListDensityMode(value) ? value : fallback;
}

/**
 * Safe getter for VehicleDisplayMode with fallback
 */
export function getVehicleDisplayMode(value: string, fallback: VehicleDisplayMode = 'licensePlate'): VehicleDisplayMode {
  return isVehicleDisplayMode(value) ? value : fallback;
}

/**
 * Safe getter for Task from unknown object
 */
export function getTask(obj: any): Task | null {
  return isTask(obj) ? obj : null;
}

/**
 * Safe getter for Task with vehicles from unknown object
 */
export function getTaskWithVehicles(obj: any): (Task & { vehicles: Vehicle[] }) | null {
  return isTaskWithVehicles(obj) ? obj : null;
}

/**
 * Safe getter for CustomColumnDefinition from unknown object
 */
export function getCustomColumnDefinition(obj: any): CustomColumnDefinition | null {
  return isCustomColumnDefinition(obj) ? obj : null;
}

/**
 * Type guard for checking if a value is a valid JSON string
 */
export function isValidJsonString(value: string): boolean {
  try {
    JSON.parse(value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Safe JSON parser that returns null on invalid JSON
 */
export function safeJsonParse<T = any>(value: string): T | null {
  try {
    return JSON.parse(value) as T;
  } catch {
    return null;
  }
}

/**
 * Convert ColumnTextStyles to Record<StyleableColumnId, ColumnTextStyle | undefined>
 * This is needed because ColumnTextStyles has optional keys while Record requires all keys
 */
export function convertColumnTextStyles(styles: ColumnTextStyles): Record<StyleableColumnId, ColumnTextStyle | undefined> {
  const validIds: StyleableColumnId[] = [
    'messages', 'dispatchReminder', 'taskNumber', 'projectName', 'projectAbbreviation',
    'constructionUnit', 'constructionSite', 'strength', 'pouringMethod', 'vehicleCount',
    'completedProgress', 'requiredVolume', 'completedVolume', 'pumpTruck', 'otherRequirements',
    'contactPhone', 'supplyTime', 'supplyDate', 'publishDate', 'dispatchedVehicles', 'productionLines'
  ];

  const result = {} as Record<StyleableColumnId, ColumnTextStyle | undefined>;

  validIds.forEach(id => {
    result[id] = styles[id];
  });

  return result;
}
