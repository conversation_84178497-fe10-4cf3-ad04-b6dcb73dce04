// src/components/sections/task-list/task-list-modals.tsx
'use client';

import React, { FC } from 'react';
import {
  Task,
  Vehicle,
  CustomColumnDefinition,
  ColumnTextStyle,
  StyleableColumnId,
  InTaskVehicleCardStyle,
  TaskListStoredSettings,
} from '@/types';
import { TankerDispatchNoteModal } from '@/components/modals/tanker-dispatch-note-modal';
import { ColumnVisibilityModal } from '@/components/modals/column-visibility-modal';
import { ColumnSpecificStyleModal } from '@/components/modals/column-specific-style-modal';
import { VehicleCardStylerModal } from '@/components/modals/vehicle-card-styler-modal';
import { DeliveryOrderDetailsModal } from '@/components/modals/delivery-order-details-modal';
import { TaskReminderConfigModal } from '@/components/modals/task-reminder-config-modal';
import { QRCodeModal } from '@/components/modals/qr-code-modal';

interface TaskListModalsProps {
  // Tanker Note Modal
  isTankerNoteModalOpen: boolean;
  closeTankerNoteModal: () => void;
  selectedTaskForTankerNote: Task | null;

  // Column Visibility Modal
  isColumnVisibilityModalOpen: boolean;
  closeColumnVisibilityModal: () => void;
  allColumns: CustomColumnDefinition[];
  columnVisibility: Record<string, boolean>;
  handleColumnVisibilityChange: (columnId: string, checked: boolean) => void;
  currentOrder: string[];
  handleColumnOrderChange: (newOrder: string[]) => void;

  // Column Specific Style Modal
  isColumnSpecificStyleModalOpen: boolean;
  closeColumnSpecificStyleModal: () => void;
  editingColumnDef: CustomColumnDefinition | null;
  columnTextStyles: Record<StyleableColumnId, ColumnTextStyle | undefined>;
  columnBackgrounds: Record<string, string>;
  handleColumnTextStyleChange: (columnId: StyleableColumnId, property: keyof ColumnTextStyle, value: string) => void;
  handleColumnBackgroundChange: (columnId: string, value: string) => void;

  // Vehicle Card Styler Modal (InTaskVehicleCardStyle)
  isStyleEditorModalOpen: boolean;
  closeStyleEditorModal: () => void;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  updateSetting: <K extends keyof TaskListStoredSettings>(key: K, value: TaskListStoredSettings[K]) => void;
  onVehiclesPerRowChange?: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;

  // Delivery Order Details Modal
  isDeliveryOrderDetailsModalOpen: boolean;
  closeDeliveryOrderDetailsModal: () => void;
  selectedVehicleForDeliveryOrder: Vehicle | null;
  selectedTaskForDeliveryOrder: Task | null;
  
  // 任务提醒设置模态窗口
  isReminderConfigModalOpen: boolean;
  closeReminderConfigModal: () => void;
  selectedTaskForReminderConfig: Task | null;

  // 二维码模态窗口
  isQRCodeModalOpen: boolean;
  closeQRCodeModal: () => void;
  selectedTaskForQRCode: Task | null;
}

export const TaskListModals: FC<TaskListModalsProps> = ({
  isTankerNoteModalOpen,
  closeTankerNoteModal,
  selectedTaskForTankerNote,
  isColumnVisibilityModalOpen,
  closeColumnVisibilityModal,
  allColumns,
  columnVisibility,
  handleColumnVisibilityChange,
  currentOrder,
  handleColumnOrderChange,
  isColumnSpecificStyleModalOpen,
  closeColumnSpecificStyleModal,
  editingColumnDef,
  columnTextStyles,
  columnBackgrounds,
  handleColumnTextStyleChange,
  handleColumnBackgroundChange,
  isStyleEditorModalOpen,
  closeStyleEditorModal,
  inTaskVehicleCardStyles,
  updateSetting,
  isDeliveryOrderDetailsModalOpen,
  closeDeliveryOrderDetailsModal,
  selectedVehicleForDeliveryOrder,
  selectedTaskForDeliveryOrder,
  isReminderConfigModalOpen,
  closeReminderConfigModal,
  selectedTaskForReminderConfig,
  isQRCodeModalOpen,
  closeQRCodeModal,
  selectedTaskForQRCode,
  onVehiclesPerRowChange,
}) => {
  return (
    <>
      {/* 各种模态窗口 */}
      <TankerDispatchNoteModal
        isOpen={isTankerNoteModalOpen}
        onOpenChange={closeTankerNoteModal}
        task={selectedTaskForTankerNote}
      />
      
      <ColumnVisibilityModal
        isOpen={isColumnVisibilityModalOpen}
        onOpenChange={closeColumnVisibilityModal}
        allColumns={allColumns}
        columnVisibility={columnVisibility}
        onColumnVisibilityChange={handleColumnVisibilityChange}
        currentOrder={currentOrder}
        onOrderChange={handleColumnOrderChange}
      />
      
      <ColumnSpecificStyleModal
        isOpen={isColumnSpecificStyleModalOpen}
        onOpenChange={closeColumnSpecificStyleModal}
        columnDef={editingColumnDef}
        currentTextStyles={editingColumnDef ? columnTextStyles[editingColumnDef.id as StyleableColumnId] : undefined}
        currentBackgroundSetting={editingColumnDef ? columnBackgrounds[editingColumnDef.id] : undefined}
        onTextStyleChange={(property, value) => {
          if (editingColumnDef) {
            handleColumnTextStyleChange(editingColumnDef.id as StyleableColumnId, property, value);
          }
        }}
        onBackgroundColorChange={(value) => {
          if (editingColumnDef) {
            handleColumnBackgroundChange(editingColumnDef.id, value);
          }
        }}
      />
      
      <VehicleCardStylerModal
        isOpen={isStyleEditorModalOpen}
        onOpenChange={closeStyleEditorModal}
        currentStyles={inTaskVehicleCardStyles}
        onStylesChange={(newStyles) => {
          updateSetting('inTaskVehicleCardStyles', newStyles);
        }}
        onVehiclesPerRowChange={onVehiclesPerRowChange}
      />
      
      <DeliveryOrderDetailsModal
        isOpen={isDeliveryOrderDetailsModalOpen}
        onOpenChange={closeDeliveryOrderDetailsModal}
        vehicle={selectedVehicleForDeliveryOrder}
        task={selectedTaskForDeliveryOrder}
      />
      
      <TaskReminderConfigModal
        isOpen={isReminderConfigModalOpen}
        onOpenChange={closeReminderConfigModal}
        task={selectedTaskForReminderConfig}
      />

      <QRCodeModal
        isOpen={isQRCodeModalOpen}
        onOpenChange={closeQRCodeModal}
        task={selectedTaskForQRCode}
      />
    </>
  );
};
