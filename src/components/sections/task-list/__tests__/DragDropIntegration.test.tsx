/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DragDropProvider } from '@/contexts/DragDropContext';
import { EnhancedTaskCardView } from '../EnhancedTaskCardView';
import type { Task, Vehicle, TaskListStoredSettings, VehicleDisplayMode } from '@/types';

// Mock the hooks and stores
jest.mock('@/store/appStore', () => ({
  useAppStore: jest.fn(() => ({
    tasks: [],
    vehicles: [],
    dispatchVehicleToTask: jest.fn(),
    reorderVehiclesInList: jest.fn(),
  })),
}));

jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(() => ({
    toast: jest.fn(),
  })),
}));

// Mock data
const mockTasks: Task[] = [
  {
    id: 'task-1',
    taskNumber: 'T001',
    constructionSite: '测试工地1',
    strength: 'C30',
    status: 'InProgress',
    requiredVolume: 100,
    completedVolume: 50,
    productionLines: ['L1', 'L2'],
  } as Task,
  {
    id: 'task-2',
    taskNumber: 'T002',
    constructionSite: '测试工地2',
    strength: 'C25',
    status: 'ReadyToProduce',
    requiredVolume: 80,
    completedVolume: 0,
    productionLines: ['L1'],
  } as Task,
];

const mockVehicles: Vehicle[] = [
  {
    id: 'vehicle-1',
    vehicleNumber: 'V001',
    status: 'pending',
    operationalStatus: 'normal',
    productionStatus: 'producing',
  } as Vehicle,
  {
    id: 'vehicle-2',
    vehicleNumber: 'V002',
    status: 'outbound',
    operationalStatus: 'normal',
    productionStatus: 'produced',
    assignedTaskId: 'task-1',
  } as Vehicle,
];

const mockSettings: TaskListStoredSettings = {
  density: 'normal',
  listFieldStyles: {},
  listModuleStyles: {},
  listLayoutConfig: {},
  updateSetting: jest.fn(),
} as any;

const defaultProps = {
  filteredTasks: mockTasks,
  vehicles: mockVehicles,
  settings: mockSettings,
  productionLineCount: 2,
  vehicleDisplayMode: 'licensePlate' as VehicleDisplayMode,
  taskStatusFilter: 'all',
  onCancelVehicleDispatch: jest.fn(),
  onOpenDeliveryOrderDetailsForVehicle: jest.fn(),
  onOpenVehicleCardContextMenu: jest.fn(),
  onTaskContextMenu: jest.fn(),
  onTaskDoubleClick: jest.fn(),
  onOpenStyleEditor: jest.fn(),
};

describe('DragDropIntegration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render DragDropProvider without errors', () => {
    render(
      <DragDropProvider>
        <div>Test Content</div>
      </DragDropProvider>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should render EnhancedTaskCardView with DragDropProvider', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskCardView {...defaultProps} />
      </DragDropProvider>
    );

    // 检查是否渲染了任务卡片
    expect(screen.getByText('任务卡片 (2)')).toBeInTheDocument();

    // 检查是否渲染了卡片配置按钮
    expect(screen.getByText('卡片配置')).toBeInTheDocument();
  });

  it('should display tasks with their information', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskCardView {...defaultProps} />
      </DragDropProvider>
    );

    // 检查任务信息是否显示
    expect(screen.getByText('T001')).toBeInTheDocument();
    expect(screen.getByText('T002')).toBeInTheDocument();
  });

  // Remove this test as it's redundant with the one above

  it('should handle card configuration modal', () => {
    render(
      <DragDropProvider>
        <EnhancedTaskCardView {...defaultProps} />
      </DragDropProvider>
    );

    // 点击卡片配置按钮
    const configButton = screen.getByText('卡片配置');
    fireEvent.click(configButton);

    // 检查是否打开了配置模态框
    // 注意：这里可能需要根据实际的模态框实现来调整测试
  });
});

describe('DragDropProvider Context', () => {
  it('should provide drag drop context to children', () => {
    const TestComponent = () => {
      // 这里可以添加对 useDragDropContext 的测试
      return <div>Context Test</div>;
    };

    render(
      <DragDropProvider>
        <TestComponent />
      </DragDropProvider>
    );

    expect(screen.getByText('Context Test')).toBeInTheDocument();
  });
});

// 性能测试
describe('Performance Tests', () => {
  it('should render large number of tasks efficiently', () => {
    const largeTasks = Array.from({ length: 100 }, (_, i) => ({
      id: `task-${i}`,
      taskNumber: `T${i.toString().padStart(3, '0')}`,
      constructionSite: `测试工地${i}`,
      strength: 'C30',
      status: 'InProgress',
      requiredVolume: 100,
      completedVolume: 50,
      productionLines: ['L1'],
    })) as Task[];

    const startTime = performance.now();
    
    render(
      <DragDropProvider>
        <EnhancedTaskCardView
          {...defaultProps}
          filteredTasks={largeTasks}
        />
      </DragDropProvider>
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // 渲染时间应该在合理范围内（比如小于1000ms）
    expect(renderTime).toBeLessThan(1000);

    // 检查是否正确显示任务数量
    expect(screen.getByText('任务卡片 (100)')).toBeInTheDocument();
  });

  it('should render large number of vehicles efficiently', () => {
    const largeVehicles = Array.from({ length: 50 }, (_, i) => ({
      id: `vehicle-${i}`,
      vehicleNumber: `V${i.toString().padStart(3, '0')}`,
      status: 'pending',
      operationalStatus: 'normal',
      productionStatus: 'producing',
    })) as Vehicle[];

    const startTime = performance.now();
    
    render(
      <DragDropProvider>
        <EnhancedTaskCardView
          {...defaultProps}
          vehicles={largeVehicles}
        />
      </DragDropProvider>
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // 渲染时间应该在合理范围内
    expect(renderTime).toBeLessThan(1000);

    // 检查是否正确显示任务数量
    expect(screen.getByText('任务卡片 (2)')).toBeInTheDocument();
  });
});
