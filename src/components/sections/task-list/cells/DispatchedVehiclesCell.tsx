// src/components/sections/task-list/cells/DispatchedVehiclesCell.tsx
'use client';

import React, { memo, useMemo } from 'react';
import type { Task, Vehicle, VehicleDisplayMode, InTaskVehicleCardStyle, TaskListDensityMode } from '@/types';
import { InTaskVehicleCard } from '../in-task-vehicle-card';
import { cn } from '@/lib/utils';

interface DispatchedVehiclesCellProps {
  task: Task;
  taskVehicles: Vehicle[];
  vehicleDisplayMode: VehicleDisplayMode;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  productionLineCount: number;
  density: Exclude<TaskListDensityMode, '' | 'card'>; 
  onCancelDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicle: Vehicle, task: Task) => void;
  onOpenContextMenu: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

const DispatchedVehiclesCellComponent: React.FC<DispatchedVehiclesCellProps> = ({
  task,
  taskVehicles,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  density, 
  onCancelDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenContextMenu
}) => {
  if (task.dispatchStatus !== 'InProgress') {
    return <div className="text-center text-xs py-1 text-muted-foreground">-</div>;
  }

  const layoutClasses = useMemo(() => {
    switch (density) {
      case 'compact':
        return 'gap-px py-px px-px';
      case 'loose':
        return 'gap-1 py-1 px-1';
      default: // normal
        return 'gap-0.5 py-0.5 px-0.5';
    }
  }, [density]);

  const cellClass = cn(
    'flex flex-row flex-wrap items-center overflow-x-auto overflow-y-hidden custom-thin-horizontal-scrollbar w-full h-full relative', 
    layoutClasses
  );
  
  const vehiclesToRender = taskVehicles.slice(0, inTaskVehicleCardStyles.vehiclesPerRow || 4);


  return (
    <div 
      className={cellClass}
      style={{ 
        maxWidth: '100%', 
        boxSizing: 'border-box',
      }}
      title={taskVehicles.length > 0 ? `${taskVehicles.length}辆车${inTaskVehicleCardStyles.vehiclesPerRow ? `, 每行最多显示${inTaskVehicleCardStyles.vehiclesPerRow}辆` : ''}` : '无调度车辆'}
    >
      {taskVehicles.length > 0 ? (
        <>
          <div className={cn("flex flex-row flex-nowrap items-center h-full", layoutClasses.startsWith('gap-') ? layoutClasses : 'gap-0.5')}>
            {taskVehicles.map(vehicle => (
              <InTaskVehicleCard
                key={`table-cell-vehicle-${vehicle.id}-${task.id}`}
                vehicle={vehicle}
                task={task}
                vehicleDisplayMode={vehicleDisplayMode}
                inTaskVehicleCardStyles={inTaskVehicleCardStyles} 
                productionLineCount={productionLineCount}
                onCancelDispatch={onCancelDispatch}
                onOpenStyleEditor={onOpenStyleEditor}
                onOpenDeliveryOrderDetails={() => onOpenDeliveryOrderDetails(vehicle, task)}
                onOpenContextMenu={(e) => onOpenContextMenu(e, vehicle, task)}
                density={density}
                isDispatchPanelView={false} 
                isTableCellView={true} // Indicate this is in a table cell
              />
            ))}
          </div>
        </>
      ) : (
        <span className="text-[10px] text-muted-foreground italic mx-auto self-center">无调度车辆</span>
      )}
    </div>
  );
};

export const DispatchedVehiclesCell = memo(DispatchedVehiclesCellComponent);
