// src/components/sections/task-list/cells/DispatchReminderCell.tsx
'use client';

import React, { memo } from 'react';
import type { Task } from '@/types';
import { DispatchCountdown } from '@/components/ui/dispatch-countdown';

interface DispatchReminderCellProps {
  task: Task;
  textClassName?: string;
}

const DispatchReminderCellComponent: React.FC<DispatchReminderCellProps> = ({ task, textClassName }) => {
  return (
    <div className={textClassName}>
      <DispatchCountdown
        nextScheduledTime={task.nextScheduledDispatchTime}
        lastDispatchTime={task.lastDispatchTime}
        dispatchFrequencyMinutes={task.dispatchFrequencyMinutes}
        dispatchStatus={task.dispatchStatus}
        compact={true}
        showIcon={false}
      />
    </div>
  );
};

export const DispatchReminderCell = memo(DispatchReminderCellComponent);

