// src/components/sections/task-list/hooks/use-task-list-business-logic.ts

import { useCallback } from 'react';
import { shallow } from 'zustand/shallow';
import type { Task, Vehicle } from '@/types';
import { useAppStore } from '@/store/appStore';
import { useUiStore } from '@/store/uiStore';
import { useCurrentPlantInfo } from '@/hooks/useCurrentPlantInfo';
import { useToast } from '@/hooks/use-toast';
import { useTaskListStyles } from './use-task-list-styles';

/**
 * 任务列表业务逻辑Hook
 * 负责处理任务列表的核心业务逻辑
 */
export function useTaskListBusinessLogic() {
  const { toast } = useToast();
  const { getStatusLabelProps } = useTaskListStyles();
  
  // Store actions
  const dispatchVehicleToTask = useAppStore(state => state.dispatchVehicleToTask);
  const cancelVehicleDispatch = useAppStore(state => state.cancelVehicleDispatch);
  const allTasks = useAppStore(state => state.tasks, shallow);
  
  // UI state
  const { selectedPlantId } = useUiStore();
  const { plants } = useCurrentPlantInfo();

  /**
   * 处理车辆调度到生产线
   */
  const handleDropOnProductionLine = useCallback(async (
    vehicle: Vehicle, 
    taskId: string, 
    lineId: string
  ) => {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
      toast({ 
        title: "调度失败", 
        description: "未找到目标任务。", 
        variant: "destructive" 
      });
      return;
    }

    if (task.dispatchStatus !== 'InProgress') {
      const statusLabel = getStatusLabelProps(task.dispatchStatus).label;
      toast({ 
        title: "调度失败", 
        description: `任务 "${task.taskNumber}" 当前状态为 "${statusLabel}"，无法调度车辆。`, 
        variant: "destructive" 
      });
      return;
    }

    if (!vehicle) {
      toast({ 
        title: "调度失败", 
        description: "拖拽的车辆信息丢失。", 
        variant: "destructive" 
      });
      return;
    }

    try {
      const updatedVehicleResult = await dispatchVehicleToTask(vehicle.id, taskId, lineId);

      if (updatedVehicleResult) {
        const plantName = plants.find(p => p.id === selectedPlantId)?.name || selectedPlantId || 'N/A';
        toast({
          title: '车辆已调度',
          description: `车辆 ${updatedVehicleResult.vehicleNumber} 已成功调度到任务 ${task.taskNumber} (生产线 ${lineId}, 厂区: ${plantName}).`,
        });
      } else {
        toast({
          title: "调度失败",
          description: `无法调度车辆 ${vehicle.vehicleNumber} 到生产线 ${lineId}。请重试。`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Vehicle dispatch error:', error);
      toast({
        title: "调度失败",
        description: `调度车辆时发生错误：${error instanceof Error ? error.message : '未知错误'}`,
        variant: "destructive",
      });
    }
  }, [allTasks, dispatchVehicleToTask, toast, getStatusLabelProps, plants, selectedPlantId]);

  /**
   * 处理车辆调度取消
   */
  const handleCancelVehicleDispatch = useCallback(async (vehicleId: string) => {
    try {
      const success = await cancelVehicleDispatch(vehicleId);
      if (success) {
        toast({
          title: "调度已取消",
          description: "车辆调度已成功取消。",
        });
      } else {
        toast({
          title: "取消失败",
          description: "无法取消车辆调度，请重试。",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Cancel vehicle dispatch error:', error);
      toast({
        title: "取消失败",
        description: `取消车辆调度时发生错误：${error instanceof Error ? error.message : '未知错误'}`,
        variant: "destructive",
      });
    }
  }, [cancelVehicleDispatch, toast]);

  /**
   * 验证任务是否可以接受车辆调度
   */
  const canAcceptVehicleDispatch = useCallback((task: Task): boolean => {
    return task.dispatchStatus === 'InProgress';
  }, []);

  /**
   * 验证车辆是否可以被调度
   */
  const canDispatchVehicle = useCallback((vehicle: Vehicle): boolean => {
    // 车辆必须是可用状态且未被分配
    return !vehicle.assignedTaskId && vehicle.status === 'pending' || vehicle.status === 'returned';
  }, []);

  /**
   * 获取任务的可调度车辆数量
   */
  const getAvailableVehicleCount = useCallback((task: Task): number => {
    // 这里可以根据任务的具体需求计算可调度的车辆数量
    const maxVehicles = task.vehicleCount || 1;
    const currentVehicles = task.vehicles?.length || 0;
    return Math.max(0, maxVehicles - currentVehicles);
  }, []);

  /**
   * 获取任务的完成进度
   */
  const getTaskProgress = useCallback((task: Task): number => {
    if (task.requiredVolume <= 0) return 0;
    return Math.min(100, (task.completedVolume / task.requiredVolume) * 100);
  }, []);

  /**
   * 检查任务是否逾期
   */
  const isTaskOverdue = useCallback((task: Task): boolean => {
    if (!task.supplyDate || !task.supplyTime) return false;
    
    const supplyDateTime = new Date(`${task.supplyDate} ${task.supplyTime}`);
    const now = new Date();
    
    return now > supplyDateTime && task.dispatchStatus !== 'Completed';
  }, []);

  /**
   * 获取任务的优先级
   */
  const getTaskPriority = useCallback((task: Task): 'high' | 'medium' | 'low' => {
    if (isTaskOverdue(task)) return 'high';
    if (task.dispatchStatus === 'InProgress') return 'medium';
    return 'low';
  }, [isTaskOverdue]);

  /**
   * 批量操作：取消多个车辆的调度
   */
  const handleBatchCancelDispatch = useCallback(async (vehicleIds: string[]) => {
    const results = await Promise.allSettled(
      vehicleIds.map(id => cancelVehicleDispatch(id))
    );
    
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    const failed = results.length - successful;
    
    if (successful > 0) {
      toast({
        title: "批量操作完成",
        description: `成功取消 ${successful} 个车辆调度${failed > 0 ? `，${failed} 个失败` : ''}。`,
      });
    } else {
      toast({
        title: "批量操作失败",
        description: "所有车辆调度取消操作都失败了。",
        variant: "destructive",
      });
    }
  }, [cancelVehicleDispatch, toast]);

  return {
    // 核心业务方法
    handleDropOnProductionLine,
    handleCancelVehicleDispatch,
    handleBatchCancelDispatch,
    
    // 验证方法
    canAcceptVehicleDispatch,
    canDispatchVehicle,
    
    // 计算方法
    getAvailableVehicleCount,
    getTaskProgress,
    getTaskPriority,
    
    // 状态检查
    isTaskOverdue,
    
    // 便捷属性
    hasSelectedPlant: !!selectedPlantId,
    plantName: plants.find(p => p.id === selectedPlantId)?.name || '未选择厂区',
  };
}
