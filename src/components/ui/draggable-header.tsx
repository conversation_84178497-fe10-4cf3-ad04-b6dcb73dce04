// src/components/ui/draggable-header.tsx
'use client';

import React from 'react';
import { Header, flexRender, Table as ReactTableType } from '@tanstack/react-table';
import { cn } from '@/lib/utils';
import { CustomColumnDefinition, DensityStyleValues } from '@/types';
import { useColumnDragDrop } from '@/hooks/useColumnDragDrop';
import { useColumnDragDropContext } from '@/contexts/ColumnDragDropContext';
import { ZIndexLevels } from '@/types/sticky-columns';

interface DraggableHeaderProps {
  header: Header<any, unknown>;
  children?: React.ReactNode;
  getColumnBackgroundProps?: (columnId: string, isHeader: boolean, isFixed: boolean) => { style: React.CSSProperties, className: string };
  onHeaderContextMenu?: (event: React.MouseEvent, column: CustomColumnDefinition) => void;
  onHeaderDoubleClick?: (event: React.MouseEvent, column: CustomColumnDefinition) => void;
  index: number;
  tableInstance: ReactTableType<any>;
}

/**
 * @component DraggableHeader
 * @description 表格的可拖拽表头组件
 */
export const DraggableHeader = React.memo(({ 
  header, 
  getColumnBackgroundProps, 
  onHeaderContextMenu, 
  onHeaderDoubleClick, 
  index, 
  tableInstance 
}: DraggableHeaderProps) => {
  const customDefMeta = header.column.columnDef.meta as { customDef?: CustomColumnDefinition & { densityStyles?: DensityStyleValues } } | undefined;
  const customDef = customDefMeta?.customDef;
  // 使用列拖拽上下文获取全局拖拽状态
  const { draggedColumnId, dragOverColumnId, isDragging: contextIsDragging } = useColumnDragDropContext();
  
  // 使用列拖拽钩子获取本地拖拽状态和引用
  const { ref, isDragging, isOver } = useColumnDragDrop({
    id: header.id,
    index,
  });
  
  // 确定当前列是否正在被拖拽或者是拖拽目标
  const isCurrentDragging = isDragging || draggedColumnId === header.id;
  const isCurrentOver = isOver || dragOverColumnId === header.id;
  
  // 获取列是否可重新排序的信息
  const { isColumnReorderable } = useColumnDragDropContext();
  const isReorderable = isColumnReorderable(header.id);

  let finalStyle: React.CSSProperties = {
    width: header.getSize(),
    minWidth: header.column.columnDef.minSize,
    maxWidth: header.column.columnDef.maxSize,
    cursor: isReorderable ? (isCurrentDragging ? 'grabbing' : 'grab') : 'default',
  };

  // 拖拽时的占位样式
  if (isCurrentDragging) {
    finalStyle.backgroundColor = 'hsl(var(--primary) / 0.1)';
    finalStyle.border = '2px dashed hsl(var(--primary))';
    finalStyle.opacity = '0.8';
    finalStyle.transform = 'scale(0.98)';
  }
  
  // 悬停时的样式
  if (isCurrentOver && !isCurrentDragging) {
    finalStyle.backgroundColor = 'hsl(var(--primary) / 0.05)';
    finalStyle.borderLeft = '2px solid hsl(var(--primary))';
  }
  
  let finalClassName = cn(
    "text-left font-semibold select-none whitespace-nowrap min-h-0 max-h-[20px] leading-tight relative", // Added relative for pseudo-elements
    customDef?.densityStyles?.headerPaddingX || 'px-1',
    customDef?.densityStyles?.headerPaddingY || 'py-0.5',
    customDef?.densityStyles?.headerHeight || 'h-5',
    isCurrentDragging && "ring-2 ring-primary column-dragging",
    isCurrentOver && !isCurrentDragging && "column-drag-over",
    contextIsDragging && !isCurrentDragging && !isCurrentOver && "opacity-50"
  );

  const visibleLeafColumns = tableInstance.getVisibleLeafColumns();
  let isLastVisibleLeftFixed = false;
  let isFirstVisibleRightFixed = false;

  if (customDef?.fixed) {
    finalStyle.position = 'sticky';
    let offset = 0;
    const leftFixedVisibleColumns = visibleLeafColumns.filter(col => (col.columnDef.meta as any)?.customDef?.fixed === 'left');
    const rightFixedVisibleColumns = visibleLeafColumns.filter(col => (col.columnDef.meta as any)?.customDef?.fixed === 'right');

    if (customDef.fixed === 'left') {
      isLastVisibleLeftFixed = leftFixedVisibleColumns.length > 0 && leftFixedVisibleColumns[leftFixedVisibleColumns.length - 1].id === header.id;
      for (const col of leftFixedVisibleColumns) {
        if (col.id === header.id) break;
        offset += col.getSize();
      }
      finalStyle.left = `${offset}px`;
    } else { // 'right'
      isFirstVisibleRightFixed = rightFixedVisibleColumns.length > 0 && rightFixedVisibleColumns[0].id === header.id;
      const currentIndex = rightFixedVisibleColumns.findIndex(c => c.id === header.id);
      for (let i = currentIndex + 1; i < rightFixedVisibleColumns.length; i++) {
        offset += rightFixedVisibleColumns[i].getSize();
      }
      finalStyle.right = `${offset}px`;
    }

    // 固定表头的背景应用
    const userBgProps = (getColumnBackgroundProps && customDef.isStyleable !== false)
      ? getColumnBackgroundProps(header.id, true, true)
      : { style: {}, className: '' };

    let userHasOpaqueBg = false;
    if (userBgProps.style.backgroundColor) {
      finalStyle.backgroundColor = userBgProps.style.backgroundColor; // 假设用户提供的纯色是不透明的
      userHasOpaqueBg = true;
    }
    if (userBgProps.className) {
      const bgClassFromUser = userBgProps.className.split(' ').find(cls => cls.startsWith('bg-') && !cls.includes('/') && !cls.includes('transparent'));
      if (bgClassFromUser) {
        finalClassName = cn(finalClassName, bgClassFromUser); // 添加用户的不透明背景类
        userHasOpaqueBg = true;
      }
      // 添加用户的其他非背景类
      finalClassName = cn(finalClassName, userBgProps.className.split(' ').filter(cls => !cls.startsWith('bg-') || cls.includes('/')).join(' '));
    }

    if (!userHasOpaqueBg) { // 如果用户没有提供不透明背景，则使用主题默认值
      finalStyle.backgroundColor = 'hsl(var(--fixed-column-header-background-default))';
    }
    
    // Z-Index 和阴影类应用
    if (isLastVisibleLeftFixed) {
      finalClassName = cn(finalClassName, 'sticky-shadow-caster-right');
      finalStyle.zIndex = ZIndexLevels.STICKY_HEADER; // 基本 z-index，伪元素处理阴影
    } else if (isFirstVisibleRightFixed) {
      finalClassName = cn(finalClassName, 'sticky-shadow-caster-left');
      finalStyle.zIndex = ZIndexLevels.STICKY_HEADER; // 基本 z-index
    } else {
      finalStyle.zIndex = ZIndexLevels.STICKY_HEADER;
    }

  } else { // 非固定
    const userBgProps = (getColumnBackgroundProps && customDef?.isStyleable !== false)
      ? getColumnBackgroundProps(header.id, true, false)
      : { style: {}, className: '' };
    if (userBgProps.style.backgroundColor) {
        finalStyle.backgroundColor = userBgProps.style.backgroundColor;
    }
    finalClassName = cn(finalClassName, userBgProps.className);
    finalStyle.zIndex = ZIndexLevels.TABLE_HEADER;
  }

  return (
    <th
      ref={ref}
      key={header.id}
      colSpan={header.colSpan}
      style={finalStyle}
      className={finalClassName}
      data-column-id={header.id}
      onContextMenu={customDef && onHeaderContextMenu ? (e) => onHeaderContextMenu(e, customDef) : undefined}
      onDoubleClick={customDef && onHeaderDoubleClick ? (e) => onHeaderDoubleClick(e, customDef) : undefined}
    >
      <div className={cn(
        "truncate text-ellipsis whitespace-nowrap leading-tight overflow-hidden",
         (customDef?.densityStyles?.headerFontSize || 'text-[11px]')
      )}>
        {header.isPlaceholder
          ? null
          : flexRender(
              header.column.columnDef.header,
              header.getContext()
            )}
      </div>
      {header.column.getCanResize() && (
        <div
          onMouseDown={(e) => {
            e.stopPropagation();
            // 直接使用 useReactTable 提供的处理器
            header.getResizeHandler()(e);
          }}
          onTouchStart={(e) => { e.stopPropagation(); header.getResizeHandler()(e); }}
          className={cn(
            "absolute top-0 h-full cursor-col-resize select-none touch-none w-4 pointer-events-auto group column-resize-handle", // Increased width to w-4
            header.column.id === 'dispatchedVehicles' ? 'left-0' : 'right-0',
            header.column.getIsResizing() ? "bg-primary/20" : "hover:bg-primary/10 transition-colors duration-200"
          )}
          style={{ zIndex: ZIndexLevels.TABLE_RESIZER_HANDLE }}
          title="拖动调整列宽"
          data-resize-handle={header.column.id}
        >
           <div className={cn(
            "absolute top-1/2 -translate-y-1/2 w-0.5 h-4/5 bg-slate-300 dark:bg-slate-600 opacity-0 group-hover:opacity-100 transition-opacity", // Made thicker and taller
            header.column.id === 'dispatchedVehicles' ? 'left-1/2 -translate-x-1/2' : 'right-1/2 translate-x-1/2',
            header.column.getIsResizing() ? "bg-primary opacity-100" : ""
           )} />
        </div>
      )}
    </th>
  );
}, (prevProps, nextProps) => {
  // 只有在以下情况下才重新渲染：
  // 1. header 对象发生变化
  // 2. index 发生变化
  // 3. tableInstance 发生变化
  return (
    prevProps.header === nextProps.header &&
    prevProps.index === nextProps.index &&
    prevProps.tableInstance === nextProps.tableInstance
  );
});