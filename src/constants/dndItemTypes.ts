// src/constants/dndItemTypes.ts
export const ItemTypes = {
  VEHICLE_CARD_DISPATCH: 'vehicleCardDispatch', // For vehicles dragged from dispatch panel
  TABLE_HEADER: 'tableHeader',                 // For reordering table columns
  IN_TASK_VEHICLE: 'inTaskVehicle',             // For vehicles dragged from within a task card
  CONFIG_FIELD_ITEM: 'configFieldItem',         // For reordering fields in configuration modals
} as const;

// Define a union type for all draggable item payloads if needed
// export type DraggableItem = 
//   | { type: typeof ItemTypes.VEHICLE_CARD_DISPATCH; vehicle: Vehicle; index: number; statusList: 'pending' | 'returned' }
//   | { type: typeof ItemTypes.TABLE_HEADER; id: string; index: number; }
//   | { type: typeof ItemTypes.IN_TASK_VEHICLE; vehicle: Vehicle; index: number; sourceTaskId: string; sourceProductionLineId?: string }
//   | { type: typeof ItemTypes.CONFIG_FIELD_ITEM; id: string; index: number; section: 'topFields' | 'middleFields' };
