# TMH任务调度系统 - API接口文档

## 📋 概述

本文档详细描述了TMH任务调度系统所需的所有API接口，包括请求参数、响应格式和使用场景。

## 🔗 基础信息

- **API基础URL**: `http://47.107.119.236:7033`
- **认证方式**: Bearer <PERSON> + 用户信息
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📊 接口总览

| 模块 | 接口数量 | 状态 |
|------|---------|------|
| 任务管理 | 8 | 🟡 部分实现 |
| 车辆管理 | 7 | 🟡 部分实现 |
| 配送单管理 | 8 | 🔴 待实现 |
| 搅拌站管理 | 3 | 🔴 待实现 |
| 提醒管理 | 8 | 🔴 待实现 |
| 任务消息 | 3 | 🔴 待实现 |
| 统计报表 | 6 | 🟡 部分实现 |
| 系统管理 | 4 | 🔴 待实现 |
| 语音功能 | 1 | 🔴 待实现 |
| 配置管理 | 3 | 🔴 待实现 |

## 🏗️ 任务管理模块

### 1. 获取任务列表

**接口信息**
- **方法**: `GET`
- **路径**: `/tasks` 或 `/Dispatch`
- **描述**: 获取调度任务列表，支持分页和筛选

**请求参数**
```typescript
interface GetTasksRequest {
  page?: number;           // 页码，默认1
  pageSize?: number;       // 每页数量，默认20
  status?: string;         // 任务状态筛选
  plantId?: string;        // 搅拌站ID筛选
  where?: string;          // JSON格式的查询条件
  sort?: number;           // 排序方式
  redisToken: string;      // 认证令牌
  u_n?: string;           // 用户名
  w_n?: string;           // 工作站名
}
```

**响应数据**
```typescript
interface GetTasksResponse {
  success: boolean;
  data: Task[];
  total: number;
  page: number;
  pageSize: number;
}

interface Task {
  id: string;
  plantId: string;
  taskNumber: string;
  projectName: string;
  projectAbbreviation: string;
  constructionUnit: string;
  constructionSite: string;
  strength: string;
  pouringMethod: string;
  vehicleCount: number;
  completedVolume: number;
  requiredVolume: number;
  pumpTruck: string;
  otherRequirements: string;
  contactPhone: string;
  subTheme?: string;
  dispatchNote?: string;
  supplyTime: string;
  supplyDate: string;
  publishDate: string;
  timing?: string;
  dispatchStatus: 'New' | 'ReadyToProduce' | 'RatioSet' | 'InProgress' | 'Paused' | 'Completed' | 'Cancelled';
  isTicketed?: boolean;
  dispatchFrequencyMinutes: number;
  lastDispatchTime: string;
  nextScheduledDispatchTime: string;
  isDueForDispatch: boolean;
  minutesToDispatch: number;
  dispatchReminderMinutes: number;
  constructionCompany?: string;
  constructionLocation?: string;
  productionLineCount?: number;
  customerName?: string;
  address?: string;
  notes?: string;
  createdAt?: string;
  scheduledTime?: string;
  estimatedDuration?: number;
  unreadMessageCount?: number;
  hasNewMessages?: boolean;
  messages?: TaskMessage[];
  deliveryStatus: string;
  vehicles: any;
}
```

**代码位置**: `src/services/dataService.ts:8`

### 2. 创建任务

**接口信息**
- **方法**: `POST`
- **路径**: `/tasks`
- **描述**: 创建新的调度任务

**请求参数**
```typescript
interface CreateTaskRequest {
  task: Omit<Task, 'id' | 'createdAt'>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface CreateTaskResponse {
  success: boolean;
  data: Task;
  message: string;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:131`

### 3. 修改任务

**接口信息**
- **方法**: `PUT`
- **路径**: `/tasks/{taskId}`
- **描述**: 修改任务信息

**请求参数**
```typescript
interface UpdateTaskRequest {
  taskId: string;
  task: Partial<Task>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface UpdateTaskResponse {
  success: boolean;
  data: Task;
  message: string;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:131`

### 4. 删除任务

**接口信息**
- **方法**: `DELETE`
- **路径**: `/tasks/{taskId}`
- **描述**: 删除指定任务

**请求参数**
```typescript
interface DeleteTaskRequest {
  taskId: string;
  reason?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface DeleteTaskResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:101`

### 5. 修改任务状态

**接口信息**
- **方法**: `PATCH`
- **路径**: `/tasks/{taskId}/status`
- **描述**: 更新任务状态

**请求参数**
```typescript
interface UpdateTaskStatusRequest {
  taskId: string;
  status: Task['dispatchStatus'];
  reason?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface UpdateTaskStatusResponse {
  success: boolean;
  data: Task;
  message: string;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:77-96`

### 6. 获取任务详情

**接口信息**
- **方法**: `GET`
- **路径**: `/tasks/{taskId}`
- **描述**: 获取单个任务详细信息

**请求参数**
```typescript
interface GetTaskDetailRequest {
  taskId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface GetTaskDetailResponse {
  success: boolean;
  data: Task;
}
```

**代码位置**: `src/components/sections/task-list/task-list-context-menus.tsx:56`

### 7. 获取任务生产进度

**接口信息**
- **方法**: `GET`
- **路径**: `/tasks/{taskId}/progress`
- **描述**: 获取任务生产进度统计

**请求参数**
```typescript
interface GetTaskProgressRequest {
  taskId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface GetTaskProgressResponse {
  success: boolean;
  data: {
    completedVolume: number;
    requiredVolume: number;
    percentage: number;
    vehicleCount: number;
    dispatchedVehicles: number;
  };
}
```

**代码位置**: `src/components/sections/action-bar.tsx:142`

### 8. 获取任务发车明细

**接口信息**
- **方法**: `GET`
- **路径**: `/tasks/{taskId}/dispatch-details`
- **描述**: 获取任务发车明细记录

**请求参数**
```typescript
interface GetDispatchDetailsRequest {
  taskId: string;
  date?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface DispatchDetail {
  id: string;
  vehicleId: string;
  vehicleNumber: string;
  dispatchTime: string;
  productionLineId: string;
  volume: number;
  status: string;
}

interface GetDispatchDetailsResponse {
  success: boolean;
  data: DispatchDetail[];
}
```

**代码位置**: `src/components/sections/action-bar.tsx:136`

## 🚛 车辆管理模块

### 1. 获取车辆列表

**接口信息**
- **方法**: `GET`
- **路径**: `/vehicles` 或 `/Dispatch/CarList_E`
- **描述**: 获取所有车辆信息

**请求参数**
```typescript
interface GetVehiclesRequest {
  plantId?: string;
  status?: string;
  type?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface Vehicle {
  id: string;
  vehicleNumber: string;
  plantId: string;
  status: 'pending' | 'dispatched' | 'returned' | 'maintenance';
  operationalStatus?: 'normal' | 'paused' | 'deactivated';
  productionStatus?: 'queued' | 'producing' | 'produced' | 'weighed' | 'ticketed' | 'shipped';
  assignedTaskId?: string;
  assignedProductionLineId?: string;
  driver?: string;
  capacity: number;
  lastDispatchTime?: string;
  estimatedReturnTime?: string;
  location?: string;
  notes?: string;
}

interface GetVehiclesResponse {
  success: boolean;
  data: Vehicle[];
}
```

**代码位置**: `src/services/dataService.ts:27`

### 2. 车辆调度到任务

**接口信息**
- **方法**: `PATCH`
- **路径**: `/vehicles/{vehicleId}/dispatch`
- **描述**: 将车辆调度到指定任务和生产线

**请求参数**
```typescript
interface DispatchVehicleRequest {
  vehicleId: string;
  taskId: string;
  productionLineId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface DispatchVehicleResponse {
  success: boolean;
  data: Vehicle;
  message: string;
}
```

**代码位置**: `src/services/dataService.ts:112`

### 3. 取消车辆调度

**接口信息**
- **方法**: `PATCH`
- **路径**: `/vehicles/{vehicleId}/cancel-dispatch`
- **描述**: 取消车辆调度，返回待发状态

**请求参数**
```typescript
interface CancelDispatchRequest {
  vehicleId: string;
  reason?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface CancelDispatchResponse {
  success: boolean;
  data: Vehicle;
  message: string;
}
```

**代码位置**: `src/services/dataService.ts:124`

### 4. 修改车辆状态

**接口信息**
- **方法**: `PATCH`
- **路径**: `/vehicles/{vehicleId}/status`
- **描述**: 修改车辆操作状态（暂停/停用/启用）

**请求参数**
```typescript
interface UpdateVehicleStatusRequest {
  vehicleId: string;
  operationalStatus: 'normal' | 'paused' | 'deactivated';
  reason?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface UpdateVehicleStatusResponse {
  success: boolean;
  data: Vehicle;
  message: string;
}
```

**代码位置**: `src/components/sections/vehicle-dispatch.tsx:196`

### 5. 车辆排序

**接口信息**
- **方法**: `PUT`
- **路径**: `/vehicles/reorder`
- **描述**: 更新车辆在列表中的排序

**请求参数**
```typescript
interface ReorderVehiclesRequest {
  statusList: 'pending' | 'returned';
  orderedIds: string[];
  redisToken: string;
}
```

**响应数据**
```typescript
interface ReorderVehiclesResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/store/appStore.ts:200`

### 6. 跨厂区调度确认

**接口信息**
- **方法**: `POST`
- **路径**: `/vehicles/{vehicleId}/cross-plant-dispatch`
- **描述**: 确认跨厂区车辆调度

**请求参数**
```typescript
interface CrossPlantDispatchRequest {
  vehicleId: string;
  targetPlantId: string;
  notes?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface CrossPlantDispatchResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/store/appStore.ts:37`

### 7. 获取车辆详情

**接口信息**
- **方法**: `GET`
- **路径**: `/vehicles/{vehicleId}`
- **描述**: 获取单个车辆详细信息

**请求参数**
```typescript
interface GetVehicleDetailRequest {
  vehicleId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface GetVehicleDetailResponse {
  success: boolean;
  data: Vehicle;
}
```

**代码位置**: `src/components/sections/vehicle-dispatch.tsx:318`

## 📦 配送单管理模块

### 1. 获取配送单列表

**接口信息**
- **方法**: `GET`
- **路径**: `/delivery-orders` 或 `/Deliver`
- **描述**: 获取配送单列表，按生产线分组

**请求参数**
```typescript
interface GetDeliveryOrdersRequest {
  plantId?: string;
  status?: string;
  lineId?: string;
  date?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface DeliveryOrder {
  id: string;
  plantId: string;
  productionLineId: string;
  vehicleNumber: string;
  vehicleStatusIcon?: string;
  driver: string;
  volume: number;
  strength: string;
  projectName: string;
  taskNumber: string;
  status?: 'newlyDispatched' | 'inProduction' | 'weighed' | 'shipped';
  customerName?: string;
  notes?: string;
}

interface GetDeliveryOrdersResponse {
  success: boolean;
  data: DeliveryOrder[];
}
```

**代码位置**: `src/services/dataService.ts:46`

### 2. 创建配送单

**接口信息**
- **方法**: `POST`
- **路径**: `/delivery-orders`
- **描述**: 创建新的配送单

**请求参数**
```typescript
interface CreateDeliveryOrderRequest {
  order: Omit<DeliveryOrder, 'id'>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface CreateDeliveryOrderResponse {
  success: boolean;
  data: DeliveryOrder;
  message: string;
}
```

**代码位置**: `src/components/sections/delivery-order-list.tsx:234`

### 3. 修改配送单

**接口信息**
- **方法**: `PUT`
- **路径**: `/delivery-orders/{orderId}`
- **描述**: 修改配送单信息

**请求参数**
```typescript
interface UpdateDeliveryOrderRequest {
  orderId: string;
  order: Partial<DeliveryOrder>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface UpdateDeliveryOrderResponse {
  success: boolean;
  data: DeliveryOrder;
  message: string;
}
```

**代码位置**: `src/components/sections/delivery-order-list.tsx:234`

### 4. 删除配送单

**接口信息**
- **方法**: `DELETE`
- **路径**: `/delivery-orders/{orderId}`
- **描述**: 删除指定配送单

**请求参数**
```typescript
interface DeleteDeliveryOrderRequest {
  orderId: string;
  reason?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface DeleteDeliveryOrderResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/components/sections/delivery-order-list.tsx:249`

### 5. 获取配送单详情

**接口信息**
- **方法**: `GET`
- **路径**: `/delivery-orders/{orderId}` 或 `/Deliver/{orderId}`
- **描述**: 获取配送单详细信息

**请求参数**
```typescript
interface GetDeliveryOrderDetailRequest {
  orderId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface GetDeliveryOrderDetailResponse {
  success: boolean;
  data: DeliveryOrder;
}
```

**代码位置**: `src/components/modals/delivery-order-details-modal.tsx:23`

### 6. 打印出货单

**接口信息**
- **方法**: `POST`
- **路径**: `/delivery-orders/{orderId}/print`
- **描述**: 打印配送单

**请求参数**
```typescript
interface PrintDeliveryOrderRequest {
  orderId: string;
  printOptions?: {
    copies: number;
    format: 'A4' | 'thermal';
  };
  redisToken: string;
}
```

**响应数据**
```typescript
interface PrintDeliveryOrderResponse {
  success: boolean;
  data: {
    printUrl: string;
    jobId: string;
  };
  message: string;
}
```

**代码位置**: `src/components/sections/delivery-order-list.tsx:234`

### 7. 背砂浆操作

**接口信息**
- **方法**: `PATCH`
- **路径**: `/delivery-orders/{orderId}/mortar`
- **描述**: 配送单背砂浆操作

**请求参数**
```typescript
interface MortarOperationRequest {
  orderId: string;
  mortarInfo: {
    type: string;
    volume: number;
    notes?: string;
  };
  redisToken: string;
}
```

**响应数据**
```typescript
interface MortarOperationResponse {
  success: boolean;
  data: DeliveryOrder;
  message: string;
}
```

**代码位置**: `src/components/sections/delivery-order-list.tsx:236`

### 8. 撤销出车

**接口信息**
- **方法**: `PATCH`
- **路径**: `/delivery-orders/{orderId}/cancel`
- **描述**: 撤销配送单出车

**请求参数**
```typescript
interface CancelDeliveryRequest {
  orderId: string;
  reason?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface CancelDeliveryResponse {
  success: boolean;
  data: DeliveryOrder;
  message: string;
}
```

**代码位置**: `src/components/sections/delivery-order-list.tsx:246`

## 🏭 搅拌站管理模块

### 1. 获取搅拌站列表

**接口信息**
- **方法**: `GET`
- **路径**: `/plants`
- **描述**: 获取所有搅拌站信息

**请求参数**
```typescript
interface GetPlantsRequest {
  redisToken: string;
}
```

**响应数据**
```typescript
interface Plant {
  id: string;
  name: string;
  stats: {
    completedTasks: number;
    totalTasks: number;
  };
  productionLineCount: number;
  location?: string;
  capacity?: number;
  status?: 'active' | 'inactive' | 'maintenance';
}

interface GetPlantsResponse {
  success: boolean;
  data: Plant[];
}
```

**代码位置**: `src/services/dataService.ts:65`

### 2. 获取搅拌站详情

**接口信息**
- **方法**: `GET`
- **路径**: `/plants/{plantId}`
- **描述**: 获取单个搅拌站详细信息

**请求参数**
```typescript
interface GetPlantDetailRequest {
  plantId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface GetPlantDetailResponse {
  success: boolean;
  data: Plant;
}
```

**代码位置**: `src/store/uiStore.ts:30`

### 3. 修改搅拌站信息

**接口信息**
- **方法**: `PUT`
- **路径**: `/plants/{plantId}`
- **描述**: 修改搅拌站信息

**请求参数**
```typescript
interface UpdatePlantRequest {
  plantId: string;
  plant: Partial<Plant>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface UpdatePlantResponse {
  success: boolean;
  data: Plant;
  message: string;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:151`

## 🔔 提醒管理模块

### 1. 获取提醒配置

**接口信息**
- **方法**: `GET`
- **路径**: `/reminders/configs`
- **描述**: 获取任务提醒配置列表

**请求参数**
```typescript
interface GetReminderConfigsRequest {
  taskId?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface ReminderConfig {
  taskId: string;
  enabled: boolean;
  intervalMinutes: number;
  maxReminders: number;
  reminderTypes: ('sound' | 'notification' | 'popup')[];
  customMessage?: string;
  lastUpdated: string;
}

interface GetReminderConfigsResponse {
  success: boolean;
  data: ReminderConfig[];
}
```

**代码位置**: `src/store/reminderDb.ts:38`

### 2. 创建提醒配置

**接口信息**
- **方法**: `POST`
- **路径**: `/reminders/configs`
- **描述**: 创建任务提醒配置

**请求参数**
```typescript
interface CreateReminderConfigRequest {
  config: Omit<ReminderConfig, 'lastUpdated'>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface CreateReminderConfigResponse {
  success: boolean;
  data: ReminderConfig;
  message: string;
}
```

**代码位置**: `src/store/reminderStore.ts:92`

### 3. 修改提醒配置

**接口信息**
- **方法**: `PUT`
- **路径**: `/reminders/configs/{taskId}`
- **描述**: 修改任务提醒配置

**请求参数**
```typescript
interface UpdateReminderConfigRequest {
  taskId: string;
  config: Partial<ReminderConfig>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface UpdateReminderConfigResponse {
  success: boolean;
  data: ReminderConfig;
  message: string;
}
```

**代码位置**: `src/store/reminderStore.ts:98`

### 4. 删除提醒配置

**接口信息**
- **方法**: `DELETE`
- **路径**: `/reminders/configs/{taskId}`
- **描述**: 删除任务提醒配置

**请求参数**
```typescript
interface DeleteReminderConfigRequest {
  taskId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface DeleteReminderConfigResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/store/reminderStore.ts:106`

### 5. 获取提醒消息

**接口信息**
- **方法**: `GET`
- **路径**: `/reminders/messages`
- **描述**: 获取提醒消息列表

**请求参数**
```typescript
interface GetReminderMessagesRequest {
  read?: boolean;
  type?: string;
  limit?: number;
  redisToken: string;
}
```

**响应数据**
```typescript
interface ReminderMessage {
  id: string;
  taskId: string;
  message: string;
  type: 'dispatch' | 'overdue' | 'custom';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  isRead: boolean;
  createdAt: string;
  readAt?: string;
}

interface GetReminderMessagesResponse {
  success: boolean;
  data: ReminderMessage[];
}
```

**代码位置**: `src/store/reminderDb.ts:57`

### 6. 标记消息已读

**接口信息**
- **方法**: `PATCH`
- **路径**: `/reminders/messages/{messageId}/read`
- **描述**: 标记提醒消息为已读

**请求参数**
```typescript
interface MarkMessageReadRequest {
  messageId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface MarkMessageReadResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/store/reminderStore.ts:53`

### 7. 标记全部已读

**接口信息**
- **方法**: `PATCH`
- **路径**: `/reminders/messages/read-all`
- **描述**: 标记所有提醒消息为已读

**请求参数**
```typescript
interface MarkAllReadRequest {
  redisToken: string;
}
```

**响应数据**
```typescript
interface MarkAllReadResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/store/reminderStore.ts:63`

### 8. 清空提醒消息

**接口信息**
- **方法**: `DELETE`
- **路径**: `/reminders/messages`
- **描述**: 清空所有提醒消息

**请求参数**
```typescript
interface ClearMessagesRequest {
  redisToken: string;
}
```

**响应数据**
```typescript
interface ClearMessagesResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/store/reminderStore.ts:73`

## 💬 任务消息模块

### 1. 获取任务消息

**接口信息**
- **方法**: `GET`
- **路径**: `/tasks/{taskId}/messages`
- **描述**: 获取任务相关消息

**请求参数**
```typescript
interface GetTaskMessagesRequest {
  taskId: string;
  read?: boolean;
  limit?: number;
  redisToken: string;
}
```

**响应数据**
```typescript
interface TaskMessage {
  id: string;
  taskId: string;
  content: string;
  sender: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  type: 'info' | 'warning' | 'urgent' | 'question';
}

interface GetTaskMessagesResponse {
  success: boolean;
  data: TaskMessage[];
}
```

**代码位置**: `src/types/index.ts:69`

### 2. 发送任务消息

**接口信息**
- **方法**: `POST`
- **路径**: `/tasks/{taskId}/messages`
- **描述**: 发送任务消息

**请求参数**
```typescript
interface SendTaskMessageRequest {
  taskId: string;
  message: Omit<TaskMessage, 'id' | 'timestamp' | 'isRead'>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface SendTaskMessageResponse {
  success: boolean;
  data: TaskMessage;
  message: string;
}
```

**代码位置**: `src/types/index.ts:69`

### 3. 标记消息已读

**接口信息**
- **方法**: `PATCH`
- **路径**: `/tasks/{taskId}/messages/{messageId}/read`
- **描述**: 标记任务消息为已读

**请求参数**
```typescript
interface MarkTaskMessageReadRequest {
  taskId: string;
  messageId: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface MarkTaskMessageReadResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/types/index.ts:75`

## 📊 统计报表模块

### 1. 今日完成统计

**接口信息**
- **方法**: `GET`
- **路径**: `/statistics/today-summary` 或 `/Dispatch/Fun/getTodaySum`
- **描述**: 获取今日完成情况统计

**请求参数**
```typescript
interface GetTodaySummaryRequest {
  plantId?: string;
  redisToken: string;
  u_n?: string;
  w_n?: string;
}
```

**响应数据**
```typescript
interface TodaySummaryResponse {
  success: boolean;
  data: {
    completedTasks: number;
    totalVolume: number;
    vehicleCount: number;
    dispatchedVehicles: number;
    averageDispatchTime: number;
    plantStats: {
      plantId: string;
      plantName: string;
      completedTasks: number;
      totalVolume: number;
    }[];
  };
}
```

**代码位置**: `README.md:88`

### 2. 罐车出车统计

**接口信息**
- **方法**: `GET`
- **路径**: `/statistics/tanker-dispatch` 或 `/Dispatch/car/Statics/Today`
- **描述**: 获取罐车出车统计

**请求参数**
```typescript
interface GetTankerDispatchStatsRequest {
  date?: string;
  plantId?: string;
  redisToken: string;
  u_n?: string;
  w_n?: string;
}
```

**响应数据**
```typescript
interface TankerDispatchStats {
  totalDispatches: number;
  totalVolume: number;
  averageVolume: number;
  vehicleStats: {
    vehicleId: string;
    vehicleNumber: string;
    dispatchCount: number;
    totalVolume: number;
    averageVolume: number;
  }[];
  hourlyStats: {
    hour: number;
    dispatchCount: number;
    volume: number;
  }[];
}

interface GetTankerDispatchStatsResponse {
  success: boolean;
  data: TankerDispatchStats;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:160`

### 3. 泵车出车统计

**接口信息**
- **方法**: `GET`
- **路径**: `/statistics/pump-dispatch` 或 `/Dispatch/pump/Statics/Today`
- **描述**: 获取泵车出车统计

**请求参数**
```typescript
interface GetPumpDispatchStatsRequest {
  date?: string;
  plantId?: string;
  redisToken: string;
  u_n?: string;
  w_n?: string;
}
```

**响应数据**
```typescript
interface PumpDispatchStats {
  totalDispatches: number;
  totalHours: number;
  averageHours: number;
  pumpStats: {
    pumpId: string;
    pumpNumber: string;
    dispatchCount: number;
    totalHours: number;
    averageHours: number;
  }[];
  projectStats: {
    projectName: string;
    dispatchCount: number;
    totalHours: number;
  }[];
}

interface GetPumpDispatchStatsResponse {
  success: boolean;
  data: PumpDispatchStats;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:162`

### 4. 调度工程统计

**接口信息**
- **方法**: `GET`
- **路径**: `/statistics/project-dispatch`
- **描述**: 获取调度工程统计

**请求参数**
```typescript
interface GetProjectDispatchStatsRequest {
  date?: string;
  plantId?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface ProjectDispatchStats {
  totalProjects: number;
  completedProjects: number;
  inProgressProjects: number;
  projectStats: {
    projectName: string;
    taskCount: number;
    completedTasks: number;
    totalVolume: number;
    completedVolume: number;
    progress: number;
  }[];
}

interface GetProjectDispatchStatsResponse {
  success: boolean;
  data: ProjectDispatchStats;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:161`

### 5. 司机统计

**接口信息**
- **方法**: `GET`
- **路径**: `/statistics/driver` 或 `/Dispatch/driver/Statics/Today`
- **描述**: 获取司机统计信息

**请求参数**
```typescript
interface GetDriverStatsRequest {
  date?: string;
  plantId?: string;
  redisToken: string;
  u_n?: string;
  w_n?: string;
}
```

**响应数据**
```typescript
interface DriverStats {
  totalDrivers: number;
  activeDrivers: number;
  driverStats: {
    driverId: string;
    driverName: string;
    vehicleNumber: string;
    dispatchCount: number;
    totalVolume: number;
    workingHours: number;
  }[];
}

interface GetDriverStatsResponse {
  success: boolean;
  data: DriverStats;
}
```

**代码位置**: `README.md:96`

### 6. 单车出车统计

**接口信息**
- **方法**: `GET`
- **路径**: `/statistics/vehicle/{vehicleId}`
- **描述**: 获取单个车辆出车统计

**请求参数**
```typescript
interface GetVehicleStatsRequest {
  vehicleId: string;
  date?: string;
  redisToken: string;
}
```

**响应数据**
```typescript
interface VehicleStats {
  vehicleId: string;
  vehicleNumber: string;
  totalDispatches: number;
  totalVolume: number;
  averageVolume: number;
  workingHours: number;
  dispatchHistory: {
    dispatchTime: string;
    taskNumber: string;
    projectName: string;
    volume: number;
    duration: number;
  }[];
}

interface GetVehicleStatsResponse {
  success: boolean;
  data: VehicleStats;
}
```

**代码位置**: `src/components/sections/vehicle-dispatch.tsx:318`

## 🎛️ 系统管理模块

### 1. 获取系统参数

**接口信息**
- **方法**: `GET`
- **路径**: `/system/settings`
- **描述**: 获取系统参数配置

**请求参数**
```typescript
interface GetSystemSettingsRequest {
  redisToken: string;
}
```

**响应数据**
```typescript
interface SystemSettings {
  dispatchSettings: {
    autoDispatchEnabled: boolean;
    dispatchIntervalMinutes: number;
    maxVehiclesPerTask: number;
  };
  reminderSettings: {
    defaultReminderMinutes: number;
    maxReminders: number;
    soundEnabled: boolean;
  };
  uiSettings: {
    theme: string;
    density: string;
    autoRefreshInterval: number;
  };
}

interface GetSystemSettingsResponse {
  success: boolean;
  data: SystemSettings;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:151`

### 2. 修改系统参数

**接口信息**
- **方法**: `PUT`
- **路径**: `/system/settings`
- **描述**: 修改系统参数配置

**请求参数**
```typescript
interface UpdateSystemSettingsRequest {
  settings: Partial<SystemSettings>;
  redisToken: string;
}
```

**响应数据**
```typescript
interface UpdateSystemSettingsResponse {
  success: boolean;
  data: SystemSettings;
  message: string;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:151`

### 3. 换班操作

**接口信息**
- **方法**: `POST`
- **路径**: `/system/shift-change`
- **描述**: 执行换班操作

**请求参数**
```typescript
interface ShiftChangeRequest {
  shiftInfo: {
    outgoingShift: string;
    incomingShift: string;
    handoverNotes: string;
    timestamp: string;
  };
  redisToken: string;
}
```

**响应数据**
```typescript
interface ShiftChangeResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/components/sections/action-bar.tsx:155`

### 4. 获取交接班记录

**接口信息**
- **方法**: `GET`
- **路径**: `/system/shift-logs`
- **描述**: 获取交接班记录

**请求参数**
```typescript
interface GetShiftLogsRequest {
  date?: string;
  limit?: number;
  redisToken: string;
}
```

**响应数据**
```typescript
interface ShiftLog {
  id: string;
  outgoingShift: string;
  incomingShift: string;
  handoverTime: string;
  handoverNotes: string;
  tasksSummary: {
    completedTasks: number;
    pendingTasks: number;
    issues: string[];
  };
}

interface GetShiftLogsResponse {
  success: boolean;
  data: ShiftLog[];
}
```

**代码位置**: `src/components/sections/action-bar.tsx:156`

## 🎵 语音功能模块

### 1. 朗读车辆进站

**接口信息**
- **方法**: `POST`
- **路径**: `/voice/announce-arrival`
- **描述**: 朗读车辆进站信息

**请求参数**
```typescript
interface AnnounceArrivalRequest {
  plantId: string;
  vehicleNumber: string;
  message: string;
  voiceSettings?: {
    speed: number;
    volume: number;
    voice: string;
  };
  redisToken: string;
}
```

**响应数据**
```typescript
interface AnnounceArrivalResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/components/modals/announce-vehicle-arrival-modal.tsx:18`

## 🔧 配置管理模块

### 1. 导出UI配置

**接口信息**
- **方法**: `GET`
- **路径**: `/config/ui/export`
- **描述**: 导出UI配置数据

**请求参数**
```typescript
interface ExportUIConfigRequest {
  redisToken: string;
}
```

**响应数据**
```typescript
interface ExportUIConfigResponse {
  success: boolean;
  data: {
    config: any; // TaskListStoredSettings
    exportTime: string;
    version: string;
  };
}
```

**代码位置**: `src/hooks/useTaskListSettings.ts:434`

### 2. 导入UI配置

**接口信息**
- **方法**: `POST`
- **路径**: `/config/ui/import`
- **描述**: 导入UI配置数据

**请求参数**
```typescript
interface ImportUIConfigRequest {
  config: any; // TaskListStoredSettings
  overwrite?: boolean;
  redisToken: string;
}
```

**响应数据**
```typescript
interface ImportUIConfigResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/hooks/useTaskListSettings.ts:452`

### 3. 重置UI配置

**接口信息**
- **方法**: `DELETE`
- **路径**: `/config/ui/reset`
- **描述**: 重置UI配置为默认值

**请求参数**
```typescript
interface ResetUIConfigRequest {
  redisToken: string;
}
```

**响应数据**
```typescript
interface ResetUIConfigResponse {
  success: boolean;
  message: string;
}
```

**代码位置**: `src/hooks/useTaskListSettings.ts:360`

## 表格梳理接口

| 功能模块      | 接口名称               | HTTP方法 | 接口路径                           | 功能描述                                   | Request参数                          | Response参数                              | 代码位置                                      |
|---------------|------------------------|----------|------------------------------------|--------------------------------------------|--------------------------------------|------------------------------------------|---------------------------------------------|
| 🏗️ 任务管理   | 获取任务列表           | GET      | /tasks                            | 获取调度任务列表，支持分页和筛选           | page, pageSize, status, plantId, where | Task[]                                  | src/services/dataService.ts:8              |
|               | 创建任务               | POST     | /tasks                            | 创建新的调度任务                           | Task (不含id)                        | Task                                     | src/components/sections/action-bar.tsx:131 |
|               | 修改任务               | PUT      | /tasks/{taskId}                   | 修改任务信息                               | taskId, Task                         | Task                                     | src/components/sections/action-bar.tsx:131 |
|               | 删除任务               | DELETE   | /tasks/{taskId}                   | 删除指定任务                               | taskId                               | {success: boolean}                      | src/components/sections/action-bar.tsx:101 |
|               | 修改任务状态           | PATCH    | /tasks/{taskId}/status            | 更新任务状态                               | taskId, status, reason?              | Task                                     | src/components/sections/action-bar.tsx:77-96 |
|               | 获取任务详情           | GET      | /tasks/{taskId}                   | 获取单个任务详细信息                       | taskId                               | Task                                     | src/components/sections/task-list/task-list-context-menus.tsx:56 |
|               | 获取任务生产进度       | GET      | /tasks/{taskId}/progress          | 获取任务生产进度统计                       | taskId                               | {completedVolume, requiredVolume, percentage} | src/components/sections/action-bar.tsx:142 |
|               | 获取任务发车明细       | GET      | /tasks/{taskId}/dispatch-details  | 获取任务发车明细记录                       | taskId                               | DispatchDetail[]                        | src/components/sections/action-bar.tsx:136 |
| 🚛 车辆管理   | 获取车辆列表           | GET      | /vehicles                         | 获取所有车辆信息                           | plantId?, status?, type?             | Vehicle[]                               | src/services/dataService.ts:27             |
|               | 车辆调度到任务         | PATCH    | /vehicles/{vehicleId}/dispatch    | 将车辆调度到指定任务和生产线               | vehicleId, taskId, productionLineId  | Vehicle                                  | src/services/dataService.ts:112            |
|               | 取消车辆调度           | PATCH    | /vehicles/{vehicleId}/cancel-dispatch | 取消车辆调度，返回待发状态                 | vehicleId, reason?                   | Vehicle                                  | src/services/dataService.ts:124            |
|               | 修改车辆状态           | PATCH    | /vehicles/{vehicleId}/status      | 修改车辆操作状态（暂停/停用/启用）         | vehicleId, operationalStatus, reason? | Vehicle                                  | src/components/sections/vehicle-dispatch.tsx:196 |
|               | 车辆排序               | PUT      | /vehicles/reorder                 | 更新车辆在列表中的排序                     | statusList, orderedIds[]             | {success: boolean}                      | src/store/appStore.ts:200                   |
|               | 跨厂区调度确认         | POST     | /vehicles/{vehicleId}/cross-plant-dispatch | 确认跨厂区车辆调度                         | vehicleId, targetPlantId, notes?     | {success: boolean}                      | src/store/appStore.ts:37                    |
|               | 获取车辆详情           | GET      | /vehicles/{vehicleId}             | 获取单个车辆详细信息                       | vehicleId                            | Vehicle                                  | src/components/sections/vehicle-dispatch.tsx:318 |
| 📦 配送单管理 | 获取配送单列表         | GET      | /delivery-orders                  | 获取配送单列表，按生产线分组               | plantId?, status?, lineId?           | DeliveryOrder[]                         | src/services/dataService.ts:46             |
|               | 创建配送单             | POST     | /delivery-orders                  | 创建新的配送单                             | DeliveryOrder (不含id)               | DeliveryOrder                           | src/components/sections/delivery-order-list.tsx:234 |
|               | 修改配送单             | PUT      | /delivery-orders/{orderId}        | 修改配送单信息                             | orderId, DeliveryOrder               | DeliveryOrder                           | src/components/sections/delivery-order-list.tsx:234 |
|               | 删除配送单             | DELETE   | /delivery-orders/{orderId}        | 删除指定配送单                             | orderId                              | {success: boolean}                      | src/components/sections/delivery-order-list.tsx:249 |
|               | 获取配送单详情         | GET      | /delivery-orders/{orderId}        | 获取配送单详细信息                         | orderId                              | DeliveryOrder                           | src/components/modals/delivery-order-details-modal.tsx:23 |
|               | 打印出货单             | POST     | /delivery-orders/{orderId}/print  | 打印配送单                                 | orderId                              | {printUrl: string}                      | src/components/sections/delivery-order-list.tsx:234 |
|               | 背砂浆操作             | PATCH    | /delivery-orders/{orderId}/mortar | 配送单背砂浆操作                           | orderId, mortarInfo                  | DeliveryOrder                           | src/components/sections/delivery-order-list.tsx:236 |
|               | 撤销出车               | PATCH    | /delivery-orders/{orderId}/cancel | 撤销配送单出车                             | orderId, reason?                     | DeliveryOrder                           | src/components/sections/delivery-order-list.tsx:246 |
| 🏭 搅拌站管理 | 获取搅拌站列表         | GET      | /plants                           | 获取所有搅拌站信息                         | 无                                   | Plant[]                                 | src/services/dataService.ts:65             |
|               | 获取搅拌站详情         | GET      | /plants/{plantId}                 | 获取单个搅拌站详细信息                     | plantId                              | Plant                                   | src/store/uiStore.ts:30                     |
|               | 修改搅拌站信息         | PUT      | /plants/{plantId}                 | 修改搅拌站信息                             | plantId, Plant                       | Plant                                   | src/components/sections/action-bar.tsx:151 |
| 🔔 提醒管理   | 获取提醒配置           | GET      | /reminders/configs                | 获取任务提醒配置列表                       | taskId?                              | ReminderConfig[]                        | src/store/reminderDb.ts:38                  |
|               | 创建提醒配置           | POST     | /reminders/configs                | 创建任务提醒配置                           | ReminderConfig (不含lastUpdated)     | ReminderConfig                          | src/store/reminderStore.ts:92               |
|               | 修改提醒配置           | PUT      | /reminders/configs/{taskId}       | 修改任务提醒配置                           | taskId, ReminderConfig               | ReminderConfig                          | src/store/reminderStore.ts:98               |
|               | 删除提醒配置           | DELETE   | /reminders/configs/{taskId}       | 删除任务提醒配置                           | taskId                               | {success: boolean}                      | src/store/reminderStore.ts:106              |
|               | 获取提醒消息           | GET      | /reminders/messages               | 获取提醒消息列表                           | read?, type?, limit?                 | ReminderMessage[]                       | src/store/reminderDb.ts:57                  |
|               | 标记消息已读           | PATCH    | /reminders/messages/{messageId}/read | 标记提醒消息为已读                         | messageId                            | {success: boolean}                      | src/store/reminderStore.ts:53               |
|               | 标记全部已读           | PATCH    | /reminders/messages/read-all      | 标记所有提醒消息为已读                     | 无                                   | {success: boolean}                      | src/store/reminderStore.ts:63               |
|               | 清空提醒消息           | DELETE   | /reminders/messages               | 清空所有提醒消息                           | 无                                   | {success: boolean}                      | src/store/reminderStore.ts:73               |
| 💬 任务消息   | 获取任务消息           | GET      | /tasks/{taskId}/messages          | 获取任务相关消息                           | taskId, read?, limit?                | TaskMessage[]                           | src/types/index.ts:69                       |
|               | 发送任务消息           | POST     | /tasks/{taskId}/messages          | 发送任务消息                               | taskId, TaskMessage (不含id)         | TaskMessage                             | src/types/index.ts:69                       |
|               | 标记消息已读           | PATCH    | /tasks/{taskId}/messages/{messageId}/read | 标记任务消息为已读                         | taskId, messageId                    | {success: boolean}                      | src/types/index.ts:75                       |
| 📊 统计报表   | 今日完成统计           | GET      | /statistics/today-summary         | 获取今日完成情况统计                       | plantId?                             | {completedTasks, totalVolume, vehicleCount} | README.md:88                                |
|               | 罐车出车统计           | GET      | /statistics/tanker-dispatch       | 获取罐车出车统计                           | date?, plantId?                      | TankerDispatchStats                     | src/components/sections/action-bar.tsx:160  |
|               | 泵车出车统计           | GET      | /statistics/pump-dispatch         | 获取泵车出车统计                           | date?, plantId?                      | PumpDispatchStats                       | src/components/sections/action-bar.tsx:162  |
|               | 调度工程统计           | GET      | /statistics/project-dispatch      | 获取调度工程统计                           | date?, plantId?                      | ProjectDispatchStats                    | src/components/sections/action-bar.tsx:161  |
|               | 司机统计               | GET      | /statistics/driver                | 获取司机统计信息                           | date?, plantId?                      | 
DriverStats                             | README.md:96                                |
|            | 单车出车统计             | GET      | /statistics/single-tanker-dispatch | 获取单车出车统计                           | date?, plantId?                      | SingleTankerDispatchStats               | README.md:104  |
| 🎛️ 系统管理   | 获取系统参数           | GET      | /system/settings                  | 获取系统参数配置                         | 无                                   | SystemSettings                          | README.md:112  |
|               | 修改系统参数           | PUT      | /system/settings                  | 修改系统参数配置                         | SystemSettings                      | SystemSettings                          | README.md:112  |
|               | 换班操作               | POST     | /system/shift-change            | 执行换班操作                             | ShiftChangeInfo                     | {success: boolean}                      | README.md:112  |
|               | 获取交接班记录         | GET      | /system/shift-logs              | 获取交接班记录                           | startDate?, endDate?                | ShiftLog[]                              | README.md:112  |


## 🚀 API替换Mock数据实施方案

### 环境配置

```bash
# .env.local
NEXT_PUBLIC_USE_MOCK_DATA=false
NEXT_PUBLIC_API_BASE_URL=http://47.107.119.236:7033
NEXT_PUBLIC_API_TIMEOUT=10000
NEXT_PUBLIC_REDIS_TOKEN=your_redis_token
NEXT_PUBLIC_USER_NAME=系统管理员
NEXT_PUBLIC_WORKSPACE_NAME=yulei-admin
```

### 实施步骤

1. **第一阶段**：核心数据接口（任务、车辆、搅拌站）
2. **第二阶段**：业务操作接口（调度、状态管理）
3. **第三阶段**：扩展功能接口（配送单、统计）
4. **第四阶段**：管理功能接口（提醒、消息、系统设置）
5. **第五阶段**：优化和完善（错误处理、性能优化）

### 数据源切换

```typescript
// 快速切换数据源
DataSourceSwitcher.setMode('api'); // 使用真实API
DataSourceSwitcher.setMode('mock'); // 使用Mock数据

// 模块级别切换
DataSourceSwitcher.setModuleMode('tasks', 'api');
DataSourceSwitcher.setModuleMode('vehicles', 'mock');
```

### 注意事项

1. **认证参数**：所有接口都需要 `redisToken` 参数
2. **错误处理**：实现完整的错误处理和回退机制
3. **数据适配**：真实API返回的数据格式可能需要适配
4. **性能优化**：实现请求缓存和防抖机制
5. **测试验证**：每个接口都需要充分测试

---

## 📝 更新日志

- **2024-12-26**: 初始版本，包含所有模块的API接口定义
- **待更新**: 根据实际API实现情况更新接口文档
