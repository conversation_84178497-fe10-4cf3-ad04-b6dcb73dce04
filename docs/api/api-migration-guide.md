# API替换Mock数据迁移指南

## 🎯 概述

本指南详细说明如何将TMH任务调度系统从Mock数据平滑迁移到真实API，确保零停机时间和最小风险。

## 🏗️ 架构设计

### 当前架构
```
Frontend Components
       ↓
   Data Services (Mock)
       ↓
   Mock Data Generator
```

### 目标架构
```
Frontend Components
       ↓
   Data Services (Unified)
       ↓
   ┌─────────────┬─────────────┐
   │ Mock Data   │ Real API    │
   │ Generator   │ Client      │
   └─────────────┴─────────────┘
```

## 🔧 核心组件实现

### 1. 统一配置管理

```typescript
// src/config/api.ts
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  useMock: boolean;
  endpoints: Record<string, string>;
  auth: {
    tokenKey: string;
    userNameKey: string;
    workspaceKey: string;
  };
}

export const getApiConfig = (): ApiConfig => {
  const useMock = process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true';
  
  return {
    baseUrl: useMock ? '' : (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://**************:7033'),
    timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000'),
    useMock,
    endpoints: {
      // 任务管理
      tasks: useMock ? '/tasks' : '/Dispatch',
      taskDetail: useMock ? '/tasks/{id}' : '/Dispatch/{id}',
      
      // 车辆管理
      vehicles: useMock ? '/vehicles' : '/Dispatch/CarList_E',
      vehicleDispatch: useMock ? '/vehicles/{id}/dispatch' : '/Dispatch/Vehicle/{id}/Assign',
      
      // 配送单管理
      deliveryOrders: useMock ? '/delivery-orders' : '/Deliver',
      deliveryOrderDetail: useMock ? '/delivery-orders/{id}' : '/Deliver/{id}',
      
      // 统计报表
      todayStats: useMock ? '/statistics/today' : '/Dispatch/Fun/getTodaySum',
      tankerStats: useMock ? '/statistics/tanker' : '/Dispatch/car/Statics/Today',
      pumpStats: useMock ? '/statistics/pump' : '/Dispatch/pump/Statics/Today',
      driverStats: useMock ? '/statistics/driver' : '/Dispatch/driver/Statics/Today',
    },
    auth: {
      tokenKey: 'redisToken',
      userNameKey: 'u_n',
      workspaceKey: 'w_n',
    }
  };
};
```

### 2. 增强的HTTP客户端

```typescript
// src/services/httpClient.ts (扩展现有实现)
export class HttpClient {
  private config: ApiConfig;

  constructor() {
    this.config = getApiConfig();
  }

  // 获取认证参数
  private getAuthParams(): Record<string, string> {
    if (this.config.useMock) return {};
    
    return {
      [this.config.auth.tokenKey]: localStorage.getItem('redisToken') || '',
      [this.config.auth.userNameKey]: localStorage.getItem('userName') || '系统管理员',
      [this.config.auth.workspaceKey]: localStorage.getItem('workspaceName') || 'yulei-admin',
    };
  }

  // 构建完整URL
  private buildUrl(endpoint: string, params?: Record<string, string>): string {
    if (this.config.useMock) return endpoint;
    
    const authParams = this.getAuthParams();
    const allParams = { ...params, ...authParams };
    const queryString = new URLSearchParams(allParams).toString();
    
    return `${this.config.baseUrl}${endpoint}${queryString ? '?' + queryString : ''}`;
  }

  // 重写请求方法
  async request<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    // Mock数据处理
    if (this.config.useMock) {
      return this.handleMockRequest<T>(endpoint, config);
    }

    // 真实API处理
    const url = this.buildUrl(endpoint, config.params);
    const response = await fetch(url, {
      method: config.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
      body: config.body ? JSON.stringify(config.body) : undefined,
      signal: AbortSignal.timeout(this.config.timeout),
    });

    if (!response.ok) {
      throw new ApiError(`HTTP ${response.status}: ${response.statusText}`, response.status);
    }

    return response.json();
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}
```

### 3. 数据适配器层

```typescript
// src/services/dataAdapters.ts (完善现有实现)
export class DataAdapters {
  // 任务数据适配
  static adaptTask(apiData: any): Task {
    // 处理真实API返回的数据格式
    if (typeof apiData === 'object' && apiData.id) {
      return {
        id: apiData.id || apiData.taskId,
        plantId: apiData.plantId || apiData.plant_id,
        taskNumber: apiData.taskNumber || apiData.task_number,
        projectName: apiData.projectName || apiData.project_name,
        dispatchStatus: this.mapDispatchStatus(apiData.status || apiData.dispatch_status),
        // ... 其他字段映射
      };
    }
    
    // 如果是Mock数据，直接返回
    return apiData as Task;
  }

  // 车辆数据适配
  static adaptVehicle(apiData: any): Vehicle {
    if (typeof apiData === 'object' && apiData.id) {
      return {
        id: apiData.id || apiData.vehicleId,
        vehicleNumber: apiData.vehicleNumber || apiData.vehicle_number,
        status: this.mapVehicleStatus(apiData.status),
        operationalStatus: this.mapOperationalStatus(apiData.operational_status),
        // ... 其他字段映射
      };
    }
    
    return apiData as Vehicle;
  }

  // 状态映射
  private static mapDispatchStatus(apiStatus: any): Task['dispatchStatus'] {
    const statusMap: Record<string, Task['dispatchStatus']> = {
      '0': 'New',
      '1': 'ReadyToProduce',
      '2': 'RatioSet',
      '3': 'InProgress',
      '4': 'Paused',
      '5': 'Completed',
      '6': 'Cancelled',
    };
    return statusMap[String(apiStatus)] || 'New';
  }

  private static mapVehicleStatus(apiStatus: any): Vehicle['status'] {
    const statusMap: Record<string, Vehicle['status']> = {
      '0': 'pending',
      '1': 'outbound',
      '2': 'returned',
    };
    return statusMap[String(apiStatus)] || 'pending';
  }

  private static mapOperationalStatus(apiStatus: any): Vehicle['operationalStatus'] {
    const statusMap: Record<string, Vehicle['operationalStatus']> = {
      '0': 'normal',
      '1': 'paused',
      '2': 'deactivated',
    };
    return statusMap[String(apiStatus)] || 'normal';
  }
}
```

### 4. 渐进式迁移服务

```typescript
// src/services/migrationService.ts
export class MigrationService {
  private static readonly MIGRATION_STATUS_KEY = 'api_migration_status';

  // 检查模块迁移状态
  static getModuleMigrationStatus(): Record<string, 'mock' | 'api' | 'hybrid'> {
    const stored = localStorage.getItem(this.MIGRATION_STATUS_KEY);
    return stored ? JSON.parse(stored) : {
      tasks: 'mock',
      vehicles: 'mock',
      deliveryOrders: 'mock',
      statistics: 'mock',
      reminders: 'mock',
      system: 'mock',
    };
  }

  // 设置模块迁移状态
  static setModuleMigrationStatus(module: string, status: 'mock' | 'api' | 'hybrid'): void {
    const current = this.getModuleMigrationStatus();
    current[module] = status;
    localStorage.setItem(this.MIGRATION_STATUS_KEY, JSON.stringify(current));
  }

  // 执行模块迁移
  static async migrateModule(module: string): Promise<boolean> {
    try {
      // 1. 检查API可用性
      const isApiHealthy = await this.checkModuleApiHealth(module);
      if (!isApiHealthy) {
        console.warn(`API for module ${module} is not healthy, keeping mock data`);
        return false;
      }

      // 2. 测试数据获取
      const testResult = await this.testModuleDataFetch(module);
      if (!testResult) {
        console.warn(`Failed to fetch test data for module ${module}`);
        return false;
      }

      // 3. 更新迁移状态
      this.setModuleMigrationStatus(module, 'api');
      console.log(`Successfully migrated module ${module} to API`);
      return true;

    } catch (error) {
      console.error(`Migration failed for module ${module}:`, error);
      this.setModuleMigrationStatus(module, 'mock');
      return false;
    }
  }

  // 检查模块API健康状态
  private static async checkModuleApiHealth(module: string): Promise<boolean> {
    const config = getApiConfig();
    if (config.useMock) return true;

    try {
      const httpClient = new HttpClient();
      return await httpClient.healthCheck();
    } catch {
      return false;
    }
  }

  // 测试模块数据获取
  private static async testModuleDataFetch(module: string): Promise<boolean> {
    try {
      switch (module) {
        case 'tasks':
          const tasks = await import('../services/dataService').then(m => m.getTasks());
          return Array.isArray(tasks) && tasks.length >= 0;
        
        case 'vehicles':
          const vehicles = await import('../services/dataService').then(m => m.getVehicles());
          return Array.isArray(vehicles) && vehicles.length >= 0;
        
        // ... 其他模块测试
        
        default:
          return true;
      }
    } catch {
      return false;
    }
  }

  // 回滚模块到Mock数据
  static rollbackModule(module: string): void {
    this.setModuleMigrationStatus(module, 'mock');
    console.log(`Rolled back module ${module} to mock data`);
  }

  // 获取迁移进度
  static getMigrationProgress(): { total: number; migrated: number; percentage: number } {
    const status = this.getModuleMigrationStatus();
    const modules = Object.keys(status);
    const migrated = modules.filter(m => status[m] === 'api').length;
    
    return {
      total: modules.length,
      migrated,
      percentage: Math.round((migrated / modules.length) * 100),
    };
  }
}
```

## 📋 迁移检查清单

### 准备阶段
- [ ] 确认API服务器可访问性
- [ ] 获取有效的认证令牌
- [ ] 配置环境变量
- [ ] 备份当前配置

### 核心模块迁移
- [ ] 任务管理模块
  - [ ] 获取任务列表
  - [ ] 任务状态更新
  - [ ] 任务详情获取
- [ ] 车辆管理模块
  - [ ] 获取车辆列表
  - [ ] 车辆调度操作
  - [ ] 车辆状态管理
- [ ] 搅拌站管理模块
  - [ ] 获取搅拌站列表
  - [ ] 搅拌站信息更新

### 扩展模块迁移
- [ ] 配送单管理
- [ ] 统计报表
- [ ] 提醒系统
- [ ] 系统设置

### 验证测试
- [ ] 功能完整性测试
- [ ] 性能基准测试
- [ ] 错误处理测试
- [ ] 用户体验测试

## 🚨 风险控制

### 回滚机制
```typescript
// 紧急回滚到Mock数据
MigrationService.rollbackModule('tasks');
MigrationService.rollbackModule('vehicles');

// 或全局回滚
DataSourceSwitcher.setMode('mock');
```

### 监控指标
- API响应时间
- 错误率
- 数据一致性
- 用户操作成功率

### 故障处理
1. **API不可用**：自动回滚到Mock数据
2. **数据格式错误**：使用数据适配器处理
3. **认证失败**：提示用户重新登录
4. **网络超时**：重试机制 + 降级处理

---

## 📞 支持联系

如有问题，请联系开发团队或查看相关文档：
- API接口文档：`docs/api/api-interfaces.md`
- 故障排除指南：`docs/troubleshooting.md`
- 开发者指南：`docs/development.md`
