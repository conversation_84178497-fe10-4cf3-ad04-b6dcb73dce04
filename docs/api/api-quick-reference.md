# TMH任务调度系统 - API接口快速参考

## 🔗 基础信息

| 项目 | 值 |
|------|-----|
| **API基础URL** | `http://**************:7033` |
| **认证方式** | Query参数: `redisToken`, `u_n`, `w_n` |
| **数据格式** | JSON |
| **超时时间** | 10秒 |

## 📊 接口总览表

| 模块 | 功能 | 方法 | 路径 | 状态 | 优先级 |
|------|------|------|------|------|--------|
| **🏗️ 任务管理** | 获取任务列表 | GET | `/Dispatch` | 🟡 部分实现 | ⭐⭐⭐ |
| | 创建任务 | POST | `/tasks` | 🔴 待实现 | ⭐⭐ |
| | 修改任务 | PUT | `/tasks/{id}` | 🔴 待实现 | ⭐⭐ |
| | 删除任务 | DELETE | `/tasks/{id}` | 🔴 待实现 | ⭐ |
| | 修改任务状态 | PATCH | `/tasks/{id}/status` | 🔴 待实现 | ⭐⭐⭐ |
| | 获取任务详情 | GET | `/tasks/{id}` | 🔴 待实现 | ⭐⭐ |
| | 获取生产进度 | GET | `/tasks/{id}/progress` | 🔴 待实现 | ⭐⭐ |
| | 获取发车明细 | GET | `/tasks/{id}/dispatch-details` | 🔴 待实现 | ⭐⭐ |
| **🚛 车辆管理** | 获取车辆列表 | GET | `/Dispatch/CarList_E` | 🟡 部分实现 | ⭐⭐⭐ |
| | 车辆调度 | PATCH | `/vehicles/{id}/dispatch` | 🔴 待实现 | ⭐⭐⭐ |
| | 取消调度 | PATCH | `/vehicles/{id}/cancel-dispatch` | 🔴 待实现 | ⭐⭐⭐ |
| | 修改车辆状态 | PATCH | `/vehicles/{id}/status` | 🔴 待实现 | ⭐⭐ |
| | 车辆排序 | PUT | `/vehicles/reorder` | 🔴 待实现 | ⭐ |
| | 跨厂区调度 | POST | `/vehicles/{id}/cross-plant-dispatch` | 🔴 待实现 | ⭐ |
| | 获取车辆详情 | GET | `/vehicles/{id}` | 🔴 待实现 | ⭐⭐ |
| **📦 配送单管理** | 获取配送单列表 | GET | `/Deliver` | 🔴 待实现 | ⭐⭐ |
| | 创建配送单 | POST | `/delivery-orders` | 🔴 待实现 | ⭐⭐ |
| | 修改配送单 | PUT | `/delivery-orders/{id}` | 🔴 待实现 | ⭐ |
| | 删除配送单 | DELETE | `/delivery-orders/{id}` | 🔴 待实现 | ⭐ |
| | 获取配送单详情 | GET | `/Deliver/{id}` | 🟡 部分实现 | ⭐⭐ |
| | 打印出货单 | POST | `/delivery-orders/{id}/print` | 🔴 待实现 | ⭐ |
| | 背砂浆操作 | PATCH | `/delivery-orders/{id}/mortar` | 🔴 待实现 | ⭐ |
| | 撤销出车 | PATCH | `/delivery-orders/{id}/cancel` | 🔴 待实现 | ⭐ |
| **🏭 搅拌站管理** | 获取搅拌站列表 | GET | `/plants` | 🔴 待实现 | ⭐⭐ |
| | 获取搅拌站详情 | GET | `/plants/{id}` | 🔴 待实现 | ⭐ |
| | 修改搅拌站信息 | PUT | `/plants/{id}` | 🔴 待实现 | ⭐ |
| **🔔 提醒管理** | 获取提醒配置 | GET | `/reminders/configs` | 🔴 待实现 | ⭐⭐ |
| | 创建提醒配置 | POST | `/reminders/configs` | 🔴 待实现 | ⭐⭐ |
| | 修改提醒配置 | PUT | `/reminders/configs/{taskId}` | 🔴 待实现 | ⭐⭐ |
| | 删除提醒配置 | DELETE | `/reminders/configs/{taskId}` | 🔴 待实现 | ⭐ |
| | 获取提醒消息 | GET | `/reminders/messages` | 🔴 待实现 | ⭐⭐ |
| | 标记消息已读 | PATCH | `/reminders/messages/{id}/read` | 🔴 待实现 | ⭐ |
| | 标记全部已读 | PATCH | `/reminders/messages/read-all` | 🔴 待实现 | ⭐ |
| | 清空提醒消息 | DELETE | `/reminders/messages` | 🔴 待实现 | ⭐ |
| **💬 任务消息** | 获取任务消息 | GET | `/tasks/{id}/messages` | 🔴 待实现 | ⭐⭐ |
| | 发送任务消息 | POST | `/tasks/{id}/messages` | 🔴 待实现 | ⭐ |
| | 标记消息已读 | PATCH | `/tasks/{id}/messages/{msgId}/read` | 🔴 待实现 | ⭐ |
| **📊 统计报表** | 今日完成统计 | GET | `/Dispatch/Fun/getTodaySum` | 🟡 部分实现 | ⭐⭐ |
| | 罐车出车统计 | GET | `/Dispatch/car/Statics/Today` | 🟡 部分实现 | ⭐⭐ |
| | 泵车出车统计 | GET | `/Dispatch/pump/Statics/Today` | 🟡 部分实现 | ⭐⭐ |
| | 调度工程统计 | GET | `/statistics/project-dispatch` | 🔴 待实现 | ⭐ |
| | 司机统计 | GET | `/Dispatch/driver/Statics/Today` | 🟡 部分实现 | ⭐ |
| | 单车出车统计 | GET | `/statistics/vehicle/{id}` | 🔴 待实现 | ⭐ |
| **🎛️ 系统管理** | 获取系统参数 | GET | `/system/settings` | 🔴 待实现 | ⭐ |
| | 修改系统参数 | PUT | `/system/settings` | 🔴 待实现 | ⭐ |
| | 换班操作 | POST | `/system/shift-change` | 🔴 待实现 | ⭐ |
| | 获取交接班记录 | GET | `/system/shift-logs` | 🔴 待实现 | ⭐ |
| **🎵 语音功能** | 朗读车辆进站 | POST | `/voice/announce-arrival` | 🔴 待实现 | ⭐ |
| **🔧 配置管理** | 导出UI配置 | GET | `/config/ui/export` | 🔴 待实现 | ⭐ |
| | 导入UI配置 | POST | `/config/ui/import` | 🔴 待实现 | ⭐ |
| | 重置UI配置 | DELETE | `/config/ui/reset` | 🔴 待实现 | ⭐ |

## 🎯 实施优先级

### 第一阶段（核心功能）⭐⭐⭐
1. **任务管理**：获取任务列表、修改任务状态
2. **车辆管理**：获取车辆列表、车辆调度、取消调度
3. **搅拌站管理**：获取搅拌站列表

### 第二阶段（业务功能）⭐⭐
1. **任务管理**：创建任务、修改任务、获取任务详情、生产进度
2. **车辆管理**：修改车辆状态、获取车辆详情
3. **配送单管理**：获取配送单列表、创建配送单、获取详情
4. **提醒管理**：获取提醒配置、创建/修改提醒配置、获取提醒消息
5. **任务消息**：获取任务消息
6. **统计报表**：今日完成统计、各类出车统计

### 第三阶段（扩展功能）⭐
1. **任务管理**：删除任务、发车明细
2. **车辆管理**：车辆排序、跨厂区调度
3. **配送单管理**：修改/删除配送单、打印、背砂浆、撤销出车
4. **其他功能**：系统管理、语音功能、配置管理

## 🔧 快速配置

### 环境变量设置
```bash
# .env.local
NEXT_PUBLIC_USE_MOCK_DATA=false
NEXT_PUBLIC_API_BASE_URL=http://**************:7033
NEXT_PUBLIC_API_TIMEOUT=10000
```

### 认证参数
```typescript
// 所有API请求都需要包含这些参数
const authParams = {
  redisToken: 'your_token_here',
  u_n: '系统管理员',
  w_n: 'yulei-admin'
};
```

### 数据源切换
```typescript
// 切换到API模式
DataSourceSwitcher.setMode('api');

// 切换到Mock模式
DataSourceSwitcher.setMode('mock');

// 模块级别切换
DataSourceSwitcher.setModuleMode('tasks', 'api');
```

## 📝 状态说明

| 状态 | 说明 |
|------|------|
| 🟢 已实现 | 接口已完全实现并测试通过 |
| 🟡 部分实现 | 接口存在但可能需要适配或完善 |
| 🔴 待实现 | 接口尚未实现，需要开发 |

## 🔗 相关文档

- [完整API接口文档](./api-interfaces.md)
- [API迁移指南](./api-migration-guide.md)
- [故障排除指南](../troubleshooting.md)

---

**最后更新**: 2024-12-26  
**文档版本**: v1.0
