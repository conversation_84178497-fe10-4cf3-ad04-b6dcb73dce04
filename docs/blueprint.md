# **App Name**: ConcreteMix Dispatch

## Core Features:

- Plant Selection: Dynamically update task and delivery lists based on the selected plant.
- Customizable Task List: Display tasks in a list with sortable columns, configurable column visibility and widths, and customizable styles, like row color, borders and lines
- Dispatch Controls: A set of buttons and select menus in the header to control task status, manage vehicles, and access settings. Each control will show a modal dialog for configuration as required.
- Drag and Drop Vehicle Dispatching: A Kanban-style interface allowing drag-and-drop dispatching of vehicles to tasks or production lines.
- Customizable Delivery Orders: Display delivery orders in a customizable, printable, and exportable format.
- Persistent Configuration: Configuration settings stored in local storage or cookies to maintain user preferences across sessions.
- Audible Feedback: Using GenAI with a Text-to-Speech tool, provide audible feedback based on events like vehicle arrival, task status changes, or system alerts. The parameters such as speech rate, language and accent are all configurable in the system parameter settings panel.

## Style Guidelines:

- Primary color: Steel blue (#4682B4) to convey reliability and efficiency, reflecting the concrete mixing process.
- Background color: Light gray (#F0F8FF), offering a clean and neutral backdrop that reduces visual fatigue.
- Accent color: Coral (#FF7F50) to highlight interactive elements and critical actions.
- Clear, sans-serif font for readability.
- Simple, consistent icons to represent task statuses and actions.
- Divided screen layout: Top (controls/summary), Left (tasks/deliveries), Right (vehicle dispatch).
- Smooth transitions and subtle animations for drag-and-drop interactions.