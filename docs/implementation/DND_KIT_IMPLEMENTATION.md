# DND Kit 车辆拖拽系统实现总结

## 概述

使用 `@dnd-kit` 重新实现了全部车辆拖拽相关操作，提供了现代化、无障碍的拖拽体验，支持触摸设备和键盘导航。

## 新架构组件

### 1. DragDropContext (`src/contexts/DragDropContext.tsx`)

**核心拖拽上下文管理器**

#### 功能特性
- 🎯 **统一状态管理**：集中管理所有拖拽状态
- 🔄 **智能拖拽类型识别**：自动识别车辆到任务、车辆到生产线、车辆重排序
- 📊 **实时状态跟踪**：跟踪拖拽源、目标、类型等信息
- ⚡ **异步操作处理**：支持拖拽完成后的异步API调用
- 🎨 **拖拽覆盖层**：提供拖拽时的视觉反馈

#### 拖拽类型支持
```typescript
type DragType = 
  | 'vehicle-to-task'           // 车辆拖拽到任务
  | 'vehicle-reorder'           // 车辆列表内重排序
  | 'vehicle-to-production-line' // 车辆拖拽到特定生产线
```

#### 传感器配置
```typescript
const sensors = useSensors(
  useSensor(PointerSensor, {
    activationConstraint: {
      distance: 8, // 8px movement required to start drag
    },
  }),
  useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  })
);
```

### 2. DraggableVehicleCard (`src/components/sections/task-list/DraggableVehicleCard.tsx`)

**可拖拽车辆卡片组件**

#### 核心特性
- 🎯 **useDraggable Hook**：使用 dnd-kit 的 useDraggable
- 📱 **触摸支持**：原生支持触摸设备
- ⌨️ **键盘导航**：支持键盘操作
- 🎨 **视觉反馈**：拖拽时的透明度和变换效果

#### 实现示例
```typescript
const {
  attributes,
  listeners,
  setNodeRef,
  transform,
  isDragging,
} = useDraggable({
  id: vehicle.id,
  data: {
    type: 'vehicle-to-task',
    vehicleId: vehicle.id,
    taskId: task?.id,
  },
});
```

### 3. DroppableVehicleList (`src/components/sections/task-list/DroppableVehicleList.tsx`)

**可放置车辆列表组件**

#### 核心特性
- 🎯 **useDroppable Hook**：使用 dnd-kit 的 useDroppable
- 📋 **SortableContext**：支持列表内排序
- 🎨 **拖拽状态指示**：悬停时的视觉反馈
- 🔄 **策略支持**：水平和垂直排序策略

#### 实现示例
```typescript
const { isOver, setNodeRef } = useDroppable({
  id: droppableId,
  data: {
    type: 'task-drop-zone',
    taskId: task?.id,
  },
});

return (
  <div ref={setNodeRef}>
    <SortableContext items={vehicleIds} strategy={strategy}>
      {/* 车辆列表 */}
    </SortableContext>
  </div>
);
```

### 4. ProductionLinePanel (`src/components/sections/task-list/cards/ProductionLinePanel.tsx`)

**生产线面板组件**

#### 核心特性
- 🏭 **生产线选择**：动态生成生产线放置区域
- 🎯 **精确放置**：每个生产线都是独立的放置目标
- 🎨 **状态指示**：拖拽时的高亮效果
- ⚡ **自动关闭**：拖拽完成后自动关闭面板

## 技术优势

### 1. 现代化架构
- **Hook-based API**：使用现代 React Hooks
- **TypeScript 支持**：完整的类型安全
- **Tree-shaking**：按需加载，减少包体积
- **无障碍支持**：内置 ARIA 属性和键盘导航

### 2. 性能优化
- **GPU 加速**：使用 transform3d 启用硬件加速
- **最小重渲染**：精确的状态管理
- **内存效率**：自动清理事件监听器
- **碰撞检测**：高效的碰撞检测算法

### 3. 用户体验
- **触摸友好**：原生支持移动设备
- **视觉反馈**：丰富的拖拽状态指示
- **平滑动画**：流畅的拖拽动画
- **错误处理**：完善的错误提示和状态回滚

## 配置兼容性

### 1. 样式配置
- 完全兼容现有的样式配置系统
- 支持字段样式、模块样式、布局配置
- 实时预览和应用配置更改

### 2. 密度设置
- 支持紧凑、标准、宽松三种密度
- 自动调整车辆卡片大小和间距
- 保持视觉一致性

### 3. 显示模式
- 支持紧凑、标准、详细三种显示模式
- 自动调整车辆信息显示内容
- 响应式布局适配

## 使用方法

### 1. 基本集成
```typescript
import { DragDropProvider } from '@/contexts/DragDropContext';

// 在应用根组件中包装
<DragDropProvider>
  <YourAppContent />
</DragDropProvider>
```

### 2. 创建可拖拽元素
```typescript
import { useDraggable } from '@dnd-kit/core';

const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
  id: 'unique-id',
  data: { type: 'vehicle-to-task', vehicleId: 'vehicle-1' },
});
```

### 3. 创建放置区域
```typescript
import { useDroppable } from '@dnd-kit/core';

const { isOver, setNodeRef } = useDroppable({
  id: 'drop-zone-id',
  data: { type: 'task-drop-zone', taskId: 'task-1' },
});
```

## 迁移指南

### 从 react-beautiful-dnd 迁移

1. **依赖更新**
```bash
npm uninstall react-beautiful-dnd @types/react-beautiful-dnd
npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities
```

2. **组件更新**
- `DragDropContext` → `DndContext`
- `Draggable` → `useDraggable`
- `Droppable` → `useDroppable`
- `provided.placeholder` → 不再需要

3. **事件处理**
- `onDragStart` → `onDragStart`
- `onDragUpdate` → `onDragOver`
- `onDragEnd` → `onDragEnd`

## 总结

新的 dnd-kit 拖拽系统提供了：

- ✅ **现代化架构**：基于 Hooks 的 API 设计
- ✅ **更好的性能**：优化的渲染和内存使用
- ✅ **无障碍支持**：内置键盘导航和屏幕阅读器支持
- ✅ **触摸友好**：原生支持移动设备
- ✅ **类型安全**：完整的 TypeScript 支持
- ✅ **灵活配置**：可自定义传感器和碰撞检测
- ✅ **向后兼容**：保持现有功能和用户体验

这个实现为车辆调度系统提供了稳定、高效、用户友好的拖拽交互体验。
