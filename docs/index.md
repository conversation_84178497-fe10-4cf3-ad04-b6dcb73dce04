# 📚 TMH 任务调度系统文档中心

欢迎来到 TMH 任务调度系统的文档中心！这里包含了系统的所有技术文档、功能说明、实现指南和问题修复记录。

## 🗂️ 文档分类

### 📋 [功能特性 (Features)](./features/)
系统核心功能的设计、实现和使用说明

- **[可配置任务卡片系统](./features/CONFIGURABLE_TASK_CARD_IMPLEMENTATION.md)** 🎯
  - 完全可配置的卡片布局系统
  - 支持拖拽发车到生产线
  - 基于 react-beautiful-dnd 的拖拽实现
  - 顶部4字段 + 中间6字段 + 底部车辆区域

- **[发车提醒功能增强](./features/DISPATCH_REMINDER_ENHANCEMENT_SUMMARY.md)** 🚨
  - 发车提醒功能的完整实现
  - 优先级提升和视觉增强
  - 列表模式对齐优化

- **[增强卡片系统](./features/ENHANCED_CARD_SYSTEM_COMPLETE.md)** ✨
  - 现代化的卡片设计系统
  - 多主题支持和自定义配置
  - 性能优化和响应式设计

- **[任务列表重设计](./features/TASK_LIST_REDESIGN_SUMMARY.md)** 🔄
  - 任务列表界面的全面重设计
  - 头部移除和布局优化
  - 用户体验提升

### ⚡ [性能优化 (Performance)](./performance/)
系统性能优化相关的技术文档

- **[卡片滚动性能修复](./performance/CARD_SCROLL_PERFORMANCE_FIX.md)** 🚀
  - 解决卡片滚动卡顿问题
  - 虚拟滚动实现
  - React 性能优化技巧
  - CSS 硬件加速

- **[卡片性能优化完整方案](./performance/CARD_PERFORMANCE_OPTIMIZATION_COMPLETE.md)** 📊
  - 全面的性能优化策略
  - 内存使用优化
  - 渲染性能提升

- **[性能优化快速指南](./performance/CARD_PERFORMANCE_QUICK_GUIDE.md)** ⚡
  - 快速性能优化指南
  - 常见性能问题解决方案

### 📖 [使用指南 (Guides)](./guides/)
系统使用和配置的详细指南

- **[快速开始指南](./guides/QUICK_START_GUIDE.md)** 🚀
  - 系统快速上手指南
  - 基本功能介绍
  - 常用操作说明

- **[任务列表样式配置指南](./guides/TASK_LIST_STYLE_CONFIG_GUIDE.md)** 🎨
  - 任务列表样式自定义
  - 配置选项详解
  - 最佳实践建议

### 🔧 [技术实现 (Implementation)](./implementation/)
技术实现细节和架构说明

- **[DND Kit 拖拽实现](./implementation/DND_KIT_IMPLEMENTATION.md)** 🎯
  - 基于 @dnd-kit 的现代化拖拽系统
  - 完整的拖拽功能实现
  - 无障碍和触摸设备支持
  - 拖拽交互设计

### 🛠️ [问题修复 (Fixes)](./fixes/)
系统问题的诊断和修复记录

- **[useMemo 导入错误修复](./fixes/USEMEMO_FIX_COMPLETE.md)** 🔧
  - React Hook 导入问题修复
  - 性能优化相关错误解决

- **[样式配置修复](./fixes/STYLE_CONFIG_FINAL_FIX.md)** 🎨
  - 样式配置相关问题修复
  - CSS 兼容性问题解决

## 🎯 快速导航

### 🆕 最新功能
- [可配置任务卡片系统](./features/CONFIGURABLE_TASK_CARD_IMPLEMENTATION.md) - 全新的可配置卡片布局
- [拖拽发车功能](./features/CONFIGURABLE_TASK_CARD_IMPLEMENTATION.md#拖拽发车系统) - 直观的拖拽操作

### 🔥 热门文档
- [卡片滚动性能修复](./performance/CARD_SCROLL_PERFORMANCE_FIX.md) - 解决卡顿问题
- [快速开始指南](./guides/QUICK_START_GUIDE.md) - 新用户必读
- [增强卡片系统](./features/ENHANCED_CARD_SYSTEM_COMPLETE.md) - 现代化卡片设计

### 🚨 常见问题
- [性能问题](./performance/) - 卡顿、内存占用等性能相关问题
- [样式问题](./fixes/) - CSS 样式和配置相关问题
- [集成问题](./implementation/) - 模块集成和架构相关问题

## 📊 文档统计

| 分类 | 文档数量 | 主要内容 |
|------|----------|----------|
| **功能特性** | 7 篇 | 核心功能设计与实现 |
| **性能优化** | 4 篇 | 性能问题诊断与优化 |
| **使用指南** | 2 篇 | 用户使用和配置指南 |
| **技术实现** | 3 篇 | 技术架构和实现细节 |
| **问题修复** | 3 篇 | Bug 修复和问题解决 |
| **总计** | **19 篇** | 完整的技术文档体系 |

## 🔍 文档搜索

### 按功能搜索
- **卡片系统**: [增强卡片](./features/ENHANCED_CARD_SYSTEM_COMPLETE.md) | [可配置卡片](./features/CONFIGURABLE_TASK_CARD_IMPLEMENTATION.md)
- **拖拽功能**: [React Beautiful DND](./implementation/REACT_BEAUTIFUL_DND_IMPLEMENTATION.md) | [拖拽发车](./features/CONFIGURABLE_TASK_CARD_IMPLEMENTATION.md)
- **性能优化**: [滚动性能](./performance/CARD_SCROLL_PERFORMANCE_FIX.md) | [整体优化](./performance/CARD_PERFORMANCE_OPTIMIZATION_COMPLETE.md)
- **发车提醒**: [功能增强](./features/DISPATCH_REMINDER_ENHANCEMENT_SUMMARY.md) | [优先级提升](./features/DISPATCH_REMINDER_PRIORITY_UPGRADE.md)

### 按技术栈搜索
- **React**: [性能优化](./performance/) | [Hook 问题](./fixes/USEMEMO_FIX_COMPLETE.md)
- **CSS**: [样式配置](./fixes/) | [硬件加速](./performance/CARD_SCROLL_PERFORMANCE_FIX.md)
- **TypeScript**: [类型安全](./implementation/) | [接口设计](./features/)
- **拖拽库**: [react-beautiful-dnd](./implementation/REACT_BEAUTIFUL_DND_IMPLEMENTATION.md)

## 📝 文档贡献

### 文档规范
- 使用 Markdown 格式
- 包含清晰的标题和目录
- 提供代码示例和截图
- 标注实现状态和版本信息

### 文档分类规则
- **features/**: 新功能的设计和实现
- **performance/**: 性能相关的优化和修复
- **guides/**: 用户使用指南和教程
- **implementation/**: 技术实现细节和架构
- **fixes/**: 问题修复和 Bug 解决

## 🔗 相关资源

### 项目文档
- [项目进化步骤](./项目进化步骤.md) - 项目发展历程
- [系统蓝图](./blueprint.md) - 系统架构蓝图
- [README](../README.md) - 项目基本信息

### 外部资源
- [React 官方文档](https://react.dev/)
- [Next.js 文档](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [react-beautiful-dnd](https://github.com/atlassian/react-beautiful-dnd)

---

**📅 最后更新**: 2025年6月14日  
**📋 文档版本**: v2.0  
**👥 维护人**: 飞灵科技

> 💡 **提示**: 建议按照功能需求选择相应的文档阅读，如有问题可参考问题修复部分的相关文档。
